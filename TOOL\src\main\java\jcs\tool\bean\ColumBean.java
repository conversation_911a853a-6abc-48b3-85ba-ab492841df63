package jcs.tool.bean;

/**
 * 欄位物件。
 */
public class ColumBean {
	
	/** KEY值 **/
	String key;
	
	/** 不為KEY值且不可為NULL **/
	String notnull;

	/** 欄位英文名 **/
	String ename;
	
	/** 欄位中文名 **/
	String cname;
	
	/** 欄位型態 **/
	String type;
	
	/** 欄位註解 **/
	String info;
	
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	
	public String getNotnull() {
		return notnull;
	}
	public void setNotnull(String notnull) {
		this.notnull = notnull;
	}

	public String getEname() {
		return ename;
	}
	public void setEname(String ename) {
		this.ename = ename;
	}

	public String getCname() {
		return cname;
	}
	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}

	public String getInfo() {
		return info;
	}
	public void setInfo(String info) {
		this.info = info;
	}
}
