package com.ejcic.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * 主應用程序類，啟動 Spring Boot 應用程序。
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.ejcic", "jcs.config", "jcs.initialization", "jcs.filter", "jcs.interceptor"})
@EntityScan(basePackages = "com.bean")
@EnableJpaRepositories(basePackages = "com.dao")
public class ProjectApplication {

	/**
	 * 主方法，啟動 Spring Boot 應用程序。
	 *
	 * @param args 命令行參數
	 */
	public static void main(String[] args) {
		SpringApplication.run(ProjectApplication.class, args);
	}

}
