package jcs.repository;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.Arrays;
import java.util.Optional;

import org.springframework.core.ResolvableType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import jakarta.persistence.EntityManager;
import jcs.util.FieldUtil;
import jcs.util.SpringUtil;

/**
 * 自訂JPA DAO,共用Method。
 */
@NoRepositoryBean
public interface CustomJpaDao<T, ID extends Serializable> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {

    /**
     * 依id查詢
     *
     * @param id 識別碼
     * @return Entity 或 null
     */
    default T findOne(ID id) {
        Optional<T> o = this.findById(id);
        if (o.isPresent())
            return o.get();
        return null;
    }

    /**
     * 依id查詢，允許是否返回一個新的物件
     *
     * @param id        識別碼
     * @param nullToNew 若找不到是否建立新物件
     * @return Entity 或新建物件
     */
    @SuppressWarnings({"unchecked", "null"})
    default T findOne(ID id, boolean nullToNew) {
        Optional<T> o = this.findById(id);
        if (!o.isPresent() && nullToNew) {
            try {
                ResolvableType resolvableType = ResolvableType.forClass(getClass()).as(JpaRepository.class);
                Class<?> clazz = resolvableType.getGeneric(0).resolve();

                Constructor<?> constructor = clazz.getConstructor(FieldUtil.getTypes(id));
                return (T) constructor.newInstance(FieldUtil.getValues(id));
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage(), e);
            }
        } else {
            return (T) o.get();
        }
    }

    /**
     * 清除(Dao上的flush效果非預期,故新增此method)
     */
    default void clear() {
        EntityManager em = SpringUtil.getEntityManager();
        if (em != null) {
            em.flush();
            em.clear();
        }
    }

    /**
     * 依id刪除
     *
     * @param id 識別碼
     */
    @SuppressWarnings("deprecation")
    default void delete(ID id) {
        if (id != null) {
            this.deleteInBatch(Arrays.asList(this.findOne(id)));
            this.clear();
        }
    }

    /**
     * 將entity刪除
     *
     * @param entity 實體
     */
    @SuppressWarnings({"deprecation", "null"})
    default void delete(T entity) {
        if (entity != null) {
            this.deleteInBatch(Arrays.asList(entity));
            this.clear();
        }
    }

    /**
     * List<T>刪除
     *
     * @param entities 實體集合
     */
    @SuppressWarnings("deprecation")
    default void delete(Iterable<T> entities) {
        if (entities != null) {
            this.deleteInBatch(entities);
            this.clear();
        }
    }

    /**
     * 全部刪除
     */
    default void delete() {
        this.deleteAllInBatch();
        this.clear();
    }
}
