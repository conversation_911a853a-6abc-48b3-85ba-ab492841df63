package jcs.autoconfigure;

import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;

import jcs.datasource.DataSourceRoutingAspect;

/**
 * DataRoutingAopAutoConfiguration
 *
 * 
 * 
 */
@AutoConfiguration
@ConditionalOnClass(ProceedingJoinPoint.class)
@ConditionalOnProperty(prefix = "jcs.datasource.routing", name = "enabled", havingValue = "true")
public class DataRoutingAopAutoConfiguration {

    /**
     * 
     *
     * @return DataSourceRoutingAspect 
     */
    @Bean
    @ConditionalOnMissingBean
    public DataSourceRoutingAspect dataSourceRoutingAspect() {
        return new DataSourceRoutingAspect();
    }
}

