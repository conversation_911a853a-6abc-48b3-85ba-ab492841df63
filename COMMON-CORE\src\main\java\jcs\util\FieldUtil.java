package jcs.util;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.commons.lang.exception.ExceptionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 這是一個公用程式，提供欄位處理函數。
 */
@Slf4j
public class FieldUtil {

	private static final PropertyUtilsBean pb = new PropertyUtilsBean();

	private static final String IGNORE_REGULAR = "^(serialVersionUID)$";

	/**
	 * 取得欄位上所有的type。
	 *
	 * @param obj 來源物件（例如複合主鍵）
	 * @return 欄位型別陣列（排除 serialVersionUID）
	 */
	public static Class<?>[] getTypes(Object obj) {
		List<Class<?>> result = new ArrayList<Class<?>>();
		Field[] fields = obj.getClass().getDeclaredFields();
		for (Field field : fields)
			try {
				if (!field.getName().matches(IGNORE_REGULAR))
					result.add(field.getType());
			} catch (Exception e) {
				log.error(ExceptionUtils.getFullStackTrace(e));
			}
		return result.toArray(new Class<?>[] {});
	}

	/**
	 * 取得所有欄位值。
	 *
	 * @param obj 來源物件（例如複合主鍵）
	 * @return 欄位值陣列（排除 serialVersionUID）
	 */
	public static Object[] getValues(Object obj) {
		List<Object> result = new ArrayList<Object>();
		Field[] fields = obj.getClass().getDeclaredFields();
		for (Field field : fields)
			try {
				if (!field.getName().matches(IGNORE_REGULAR))
					result.add(pb.getProperty(obj, field.getName()));
			} catch (Exception e) {
				log.error(ExceptionUtils.getFullStackTrace(e));
			}
		return result.toArray(new Object[] {});
	}
}
