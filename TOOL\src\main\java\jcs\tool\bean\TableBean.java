package jcs.tool.bean;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.StringJoiner;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hwpf.extractor.WordExtractor;

/**
 * 表格物件。
 */
public class TableBean {
	
	public static final String Orm = "1";
	public static final String Dao = "2";
	public static final String Sql = "3";
	public static final String List = "4";
	
	/** 類別 **/
	String type;

	/** 表格名稱(英文) **/
	String ename;

	/** 表格名稱(中文) **/
	String cname;
	
	/** 結構描述 **/
	String schema;

	/** 表格空間 **/
	String tablespace;
	
	/** 是否有歷史檔 **/
	String history;
	
	/** 是否有Date欄位 **/
	String DateColum;

	/** 是否有BigDecimal欄位 **/
	String BigDecimalColum;
	
	/** 是否有不可為NULL欄位 **/
	String NullToSpaceColum;
	
	/** 表格代號 **/
	String no;

	/** 頁碼 **/
	String footer;
	
	/** KEY欄位 to String **/
	String key;

	/** Key欄位 **/
	ArrayList<ColumBean> keyColumlist;

	/** 所有欄位 **/
	ArrayList<ColumBean> columlist;
	
	/** 索引 **/
	ArrayList<IndexBean> indexlist;
	
	/** 專案參照 **/
	List<String> referenceslist;

	/** 來源位置 **/
	String from;

	/** 產出位置 **/
	String to;
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	
	public String getEname() {
		return ename;
	}
	public void setEname(String ename) {
		this.ename = ename;
	}

	public String getCname() {
		return cname;
	}
	public void setCname(String cname) {
		this.cname = cname;
	}
	
	public String getSchema() {
		return schema;
	}
	public void setSchema(String schema) {
		this.schema = schema;
	}
	
	public String getTablespace() {
		return tablespace;
	}
	public void setTablespace(String tablespace) {
		this.tablespace = tablespace;
	}

	public String getHistory() {
		return history;
	}
	public void setHistory(String history) {
		this.history = history;
	}
	
	public String getDateColum() {
		return DateColum;
	}
	public void setDateColum(String dateColum) {
		DateColum = dateColum;
	}
	
	public String getBigDecimalColum() {
		return BigDecimalColum;
	}
	public void setBigDecimalColum(String bigDecimalColum) {
		BigDecimalColum = bigDecimalColum;
	}
	
	public String getNulltoSpaceColum() {
		return NullToSpaceColum;
	}
	public void setNulltoSpace(String nullToSpaceColum) {
		NullToSpaceColum = nullToSpaceColum;
	}
	
	public String getNo() {
		return footer.substring(4, footer.lastIndexOf("-"));
	}
	public void setNo(String no) {
		this.no = no;
	}
	
	public String getFooter() {
		return footer.substring(0, footer.lastIndexOf("-"));
	}
	public void setFooter(String footer) {
		this.footer = footer;
	}
	
	public void setKey(String key) {
		this.key = key;
	}
	public String getKey(){
		StringJoiner joiner = new StringJoiner("\n");
		for(ColumBean columBean : keyColumlist){
			joiner.add(columBean.getCname());
		}
		return joiner.toString();
	}
	
	public ArrayList<ColumBean> getKeyColumlist() {
		return keyColumlist;
	}
	public void setKeyColumlist(ArrayList<ColumBean> keyColumlist) {
		this.keyColumlist = keyColumlist;
	}
	
	public ArrayList<ColumBean> getColumlist() {
		return columlist;
	}
	public void setColumlist(ArrayList<ColumBean> columlist) {
		this.columlist = columlist;
	}
	
	public ArrayList<IndexBean> getIndexlist() {
		return indexlist;
	}
	public void setIndexlist(ArrayList<IndexBean> indexlist) {
		this.indexlist = indexlist;
	}

	public List<String> getReferenceslist() {
		return referenceslist;
	}
	public void setReferenceslist(List<String> referenceslist) {
		this.referenceslist = referenceslist;
	}
	
	public String getFrom() {
		return from;
	}
	public void setFrom(String from) {
		this.from = from;
	}
	
	public String getTo() {
		return to;
	}
	public void setTo(String to) {
		this.to = to;
	}
	
	public String getOrmName(){
		if("True".equals(history) && 
				("H".equals(ename.substring(0, 1)) || "V".equals(ename.substring(0, 1)))){
			return ename.substring(0, 2) + ename.substring(2, 5).toLowerCase(Locale.getDefault())
					+ ename.substring(5, 6) + ename.substring(6).toLowerCase(Locale.getDefault());
		}else{
			return ename.substring(0, 1) + ename.substring(1, 4).toLowerCase(Locale.getDefault())
					+ ename.substring(4, 5) + ename.substring(5).toLowerCase(Locale.getDefault());
		}
	}
	
	/**
	 * 將DOC文件轉換為TableBean物件。
	 */
	@SuppressWarnings("deprecation")
	public TableBean parse(String from, String to, String exec, String type){
		FileInputStream fis = null;
		WordExtractor extractor = null;
		try{
			fis = new FileInputStream(from);
			extractor = new WordExtractor(fis);
			String[] strArray = extractor.getParagraphText();
			boolean columStart = false;
			boolean indexStart = false;
			boolean referencesStart = false; 
			this.setFrom(from);
			this.setTo(to);
			this.setType(type);
			this.setFooter(extractor.getFooterText().replaceAll("(PAGE  0|||\n)", ""));
			ArrayList<ColumBean> keyColumList = new ArrayList<ColumBean>();
			ArrayList<ColumBean> columList = new ArrayList<ColumBean>();
			ArrayList<IndexBean> indexList = new ArrayList<IndexBean>();
			ArrayList<String> referencesList = new ArrayList<String>();
			for(int i = 0; i < strArray.length; i++){
				// Parse Table
				// 移除換行符號 add by Vance
				String str = strArray[i].trim().replaceAll("", "");
				if(str.contains("表格名稱(英文)")){
					this.setEname(strArray[i + 1].trim());
				}
				if(str.contains("表格名稱(中文)")){
					this.setCname(strArray[i + 1].trim());
				}
				if(str.contains("結構描述")){
					this.setSchema(strArray[i + 1].trim());
				}
				if(str.contains("表格空間")){
					this.setTablespace(strArray[i + 1].trim());
				}
				if(str.contains("是否有歷史檔")){
					this.setHistory(strArray[i + 1].trim());
				}
				// Parse Colum
				if(str.contains("說明：")){
					columStart = false;
				}
				// ◎表示欄位中間的註解
				if(columStart && !"".equals(str) && !str.contains("◎")){
					if("".equals(strArray[i - 1].trim()) && "".equals(strArray[i - 2].trim())){
						ColumBean colum = new ColumBean();
						// *代表KEY #代表不為KEY值且不可為NULL
						if("*".equals(str.substring(0, 1))){
							colum.setKey("Y");
							colum.setNotnull("N");
							colum.setCname(str.substring(1));
						}else if("#".equals(str.substring(0, 1))){
							colum.setKey("N");
							colum.setNotnull("Y");
							colum.setCname(str.substring(1));
							this.setNulltoSpace("Y");
						}else{
							colum.setKey("N");
							colum.setNotnull("N");
							colum.setCname(str);
						}
						colum.setEname(strArray[i + 1].trim().toLowerCase(Locale.getDefault()));
						colum.setType(strArray[i + 2].trim().toUpperCase());
						colum.setInfo(strArray[i + 3].trim());
						if(colum.getType().contains("DATE") ||
								colum.getType().contains("TIMESTAMP")){
							this.setDateColum("Y");
						}else if(colum.getType().contains("DECIMAL")&&colum.getType().contains(",")){
							String decimalType = colum.getType().substring(
									colum.getType().indexOf("(") + 1,
									colum.getType().indexOf(")"));
							String[] digits = decimalType.split(",");
							if((Integer.parseInt(StringUtils.trim(digits[0])) - Integer.parseInt(StringUtils.trim(digits[1]))) > 7){
								this.setBigDecimalColum("Y");
							}
						}
						if("Y".equals(colum.getKey())){
							keyColumList.add(colum);
						}
						columList.add(colum);
					}
				}
				if(str.contains("註    解")){
					columStart = true;
				}
				// Parse Index
				if(str.contains("專案參照：") || str.contains("批次資訊：")){
					indexStart = false;
				}
				if(indexStart && !"".equals(str)){
					if("".equals(strArray[i - 1].trim()) && "".equals(strArray[i - 2].trim())){
						IndexBean index = new IndexBean();
						index.setName(str.trim());
						index.setColum(strArray[i + 1].trim());
						indexList.add(index);
					}
				}
				if(str.contains("索引備註")){
					indexStart = true;
				}
				// Parse References
				if(str.contains("修改歷程：")){
					referencesStart = false;
				}
				if(referencesStart && !"".equals(str)){
					if("".equals(strArray[i - 1].trim()) && "".equals(strArray[i - 2].trim())){
						referencesList.add(str.toUpperCase(Locale.getDefault()).trim());
					}
				}
				if(str.contains("專案(代碼)")){
					referencesStart = true;
				}
			}
			this.keyColumlist = keyColumList;
			this.columlist = columList;
			this.indexlist = indexList;
			this.referenceslist = referencesList;
			System.out.println("▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨▨");
			System.out.println("表格名稱(英文)：" + this.getEname());
			System.out.println("表格名稱(中文)：" + this.getCname());
			System.out.println("表格類別：" + this.getType());
			System.out.println("結構描述：" + this.getSchema());
			System.out.println("表格空間 ：" + this.getTablespace());
			System.out.println("是否有歷史檔：" + this.getHistory());
			System.out.println("來源位置：" + this.getFrom());
			if(Orm.equals(exec)){
				System.out.println("產出位置：" + this.getTo() + this.getOrmName() + ".java");
			}else if(Dao.equals(exec)){
				System.out.println("產出位置：" + this.getTo() + this.getOrmName() + "Dao.java");
			}else if(Sql.equals(exec) || List.equals(exec)){
				System.out.println("產出位置：" + this.getTo());
			}
			for(String references : referencesList){
				System.out.println("專案參照：" + references);
			}
			System.out.println("頁碼：" + this.getFooter());
		}catch(Exception e){
			e.printStackTrace(System.err);
		}finally{
			try{
				if(extractor != null)
					extractor.close();
				if(fis != null)
					fis.close();
			}catch(IOException e){
				e.printStackTrace(System.err);
			}
		}
		return this;
	}
	
}
