package jcs.datasource;

import java.util.ArrayDeque;
import java.util.Deque;

/**
 * 指定 DataSource 切換的執行緒區域工具。
 *
 * 提供巢狀安全（nested-safe）的 push/pop API，亦保留舊版相容 API：
 * - setCustomerType(value): 等同 push(value)
 * - getCustomerType(): 取得目前頂端（current）
 * - clearCustomerType(): 清除整個堆疊（不等於 pop）
 */
public class DataSourceHolder {
    private static final ThreadLocal<Deque<String>> CONTEXT = ThreadLocal.withInitial(ArrayDeque::new);

    /**
     * 推入一個資料來源鍵值（巢狀安全）。
     * 
     * @param type 資料來源代碼（如 "tw"、"us"）
     */
    public static void push(String type) {
        if (type == null)
            return;
        CONTEXT.get().push(type);
    }

    /**
     * 彈出目前頂端的資料來源鍵值。
     * 
     * @return 被彈出的資料來源代碼；若堆疊為空則回傳 null
     */
    public static String pop() {
        Deque<String> stack = CONTEXT.get();
        return stack.isEmpty() ? null : stack.pop();
    }

    /**
     * 取得目前的資料來源鍵值（不移除）。
     * 
     * @return 當前資料來源代碼；若無則回傳 null
     */
    public static String current() {
        Deque<String> stack = CONTEXT.get();
        return stack.isEmpty() ? null : stack.peek();
    }

    /**
     * 舊版相容：設定當前使用的資料來源鍵值（等同 push）。
     * 
     * @param type 資料來源代碼
     */
    public static void setCustomerType(String type) {
        push(type);
    }

    /**
     * 舊版相容：取得當前使用的資料來源鍵值（等同 current）。
     * 
     * @return 資料來源代碼
     */
    public static String getCustomerType() {
        return current();
    }

    /**
     * 舊版相容：清除整個堆疊（非單步 pop）。
     */
    public static void clearCustomerType() {
        CONTEXT.remove();
    }
}
