<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>tcb.jcic</groupId>
		<artifactId>tcb-ejcic-parent</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<groupId>tcb.jcic</groupId>
	<artifactId>EJMN-JPA</artifactId>
	<version>${revision}</version>
	<name>EJMN-JPA</name>
	<description>EJMN-JPA project for Spring Boot</description>
	<url />
	<licenses>
		<license />
	</licenses>
	<developers>
		<developer />
	</developers>
	<scm>
		<connection />
		<developerConnection />
		<tag />
		<url />
	</scm>
	<dependencies>
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-JCS</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- Data auto-configuration and shared JPA helpers -->
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-DATA-STARTER</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>

</project>