package com.ejcic.config;

import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

/**
 * ServletInitializer 用於配置 Spring Boot 應用程序的 Servlet 初始化。
 * 繼承自 SpringBootServletInitializer 以支持傳統的 WAR 部署。
 */
public class ServletInitializer extends SpringBootServletInitializer {

	/**
	 * 配置 Spring Boot 應用程序。
	 *
	 * @param application SpringApplicationBuilder
	 * @return SpringApplicationBuilder
	 */
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(ProjectApplication.class);
	}

}
