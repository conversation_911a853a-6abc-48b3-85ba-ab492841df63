package jcs.autoconfigure;

import java.io.IOException;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jcs.datasource.DataSourceHolder;

/**
 * DataRoutingWebAutoConfiguration
 *
 * 於 Web 
 * 
 */
@AutoConfiguration
@ConditionalOnClass(OncePerRequestFilter.class)
@EnableConfigurationProperties(DataSourceHeaderProperties.class)
public class DataRoutingWebAutoConfiguration {

    /**
     * 
     *
     * @param props
     * @return FilterRegistrationBean
     */
    @Bean
    @ConditionalOnProperty(prefix = "jcs.datasource.header", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "dataSourceHeaderFilterRegistration")
    public FilterRegistrationBean<OncePerRequestFilter> dataSourceHeaderFilterRegistration(
            DataSourceHeaderProperties props) {
        OncePerRequestFilter filter = new OncePerRequestFilter() {
            @Override
            protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                    @NonNull FilterChain filterChain)
                    throws ServletException, IOException {
                String headerName = props.getName();
                String value = request.getHeader(headerName);
                if (value == null || value.isEmpty()) {
                    filterChain.doFilter(request, response);
                    return;
                }
                // push/pop
                DataSourceHolder.push(value);
                try {
                    filterChain.doFilter(request, response);
                } finally {
                    DataSourceHolder.pop();
                }
            }
        };
        FilterRegistrationBean<OncePerRequestFilter> reg = new FilterRegistrationBean<>(filter);
        reg.setName("dataSourceHeaderFilter");
        reg.setOrder(Ordered.HIGHEST_PRECEDENCE + 20);
        return reg;
    }
}
