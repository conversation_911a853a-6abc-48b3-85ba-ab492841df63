package jcs.sitemesh3;

import org.sitemesh.SiteMeshContext;
import org.sitemesh.content.ContentProperty;
import org.sitemesh.content.tagrules.TagRuleBundle;
import org.sitemesh.content.tagrules.html.ExportTagToContentRule;
import org.sitemesh.tagprocessor.State;

/**
 * sitemesh3自定義標籤。
 */
public class ExtHtmlTagRuleBundle implements TagRuleBundle{
	public void cleanUp(State arg0, ContentProperty arg1, SiteMeshContext arg2){
	}
	
	public void install(State arg0, ContentProperty arg1, SiteMeshContext arg2){
		arg0.addRule("jcs-title", new ExportTagToContentRule(arg2, arg1.getChild("jcs-title"), false));
		arg0.addRule("jcs-page", new ExportTagToContentRule(arg2, arg1.getChild("jcs-page"), false));
		arg0.addRule("jcs-sub-title", new ExportTagToContentRule(arg2, arg1.getChild("jcs-sub-title"), false));
	}
}
