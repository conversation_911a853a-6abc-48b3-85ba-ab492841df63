{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["getSelector", "element", "selector", "getAttribute", "hrefAttr", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "j<PERSON>y", "nodeType", "getElement", "length", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "TypeError", "toUpperCase", "isVisible", "getClientRects", "getComputedStyle", "getPropertyValue", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "window", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "index", "indexOf", "listLength", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "customEventsRegex", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "i", "len", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFn", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "inNamespace", "isNamespace", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "isNative", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "BaseComponent", "constructor", "_element", "DATA_KEY", "dispose", "EVENT_KEY", "getOwnPropertyNames", "propertyName", "_queueCallback", "isAnimated", "[object Object]", "getInstance", "VERSION", "Error", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "closest", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "pageYOffset", "left", "pageXOffset", "position", "offsetTop", "offsetLeft", "SelectorEngine", "find", "concat", "Element", "prototype", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "Carousel", "super", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "activeIndex", "_getItemIndex", "order", "_handleSwipe", "absDeltax", "abs", "direction", "_keydown", "_addTouchEventListeners", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerType", "start", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "_getItemByOrder", "isNext", "_triggerSlideEvent", "eventDirectionName", "targetIndex", "fromIndex", "_setActiveIndicatorElement", "activeIndicator", "indicators", "parseInt", "elementInterval", "defaultInterval", "directionOrOrder", "_directionToOrder", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "_orderToDirection", "triggerSlidEvent", "completeCallBack", "action", "ride", "carouselInterface", "slideIndex", "dataApiClickHandler", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activesData", "actives", "container", "tempActiveData", "elemActive", "dimension", "_getDimension", "style", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected", "trigger<PERSON><PERSON>y", "isOpen", "bottom", "right", "basePlacements", "variationPlacements", "reduce", "acc", "placement", "placements", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "styles", "assign", "effect", "_ref2", "initialStyles", "popper", "options", "strategy", "margin", "arrow", "reference", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "includeScale", "scaleX", "scaleY", "offsetWidth", "width", "height", "x", "y", "getLayoutRect", "clientRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "_ref3", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "_ref3$x", "_ref3$y", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "computeStyles$1", "_ref4", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data-popper-placement", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "scrollTop", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "listScrollParents", "_element$ownerDocumen", "getScrollParent", "isBody", "visualViewport", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "data-popper-reference-hidden", "data-popper-escaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_mainSide", "_altSide", "_offset", "_min", "_max", "_preventedOffset", "getCompositeRect", "elementOrVirtualElement", "isFixed", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "DEFAULT_OPTIONS", "modifiers", "areValidElements", "_len", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "visited", "result", "modifier", "dep", "depModifier", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "REGEXP_KEYDOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "getParentFromElement", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "isDisplayStatic", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "items", "toggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isActive", "stopPropagation", "getToggleButton", "clearMenus", "dataApiKeydownHandler", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_saveInitialAttribute", "styleProp", "scrollbarWidth", "_applyManipulationCallback", "_resetElementAttributes", "actualValue", "removeProperty", "callBack", "isOverflowing", "className", "rootElement", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "trapElement", "autofocus", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_ignoreBackdropClick", "_scrollBar", "_isAnimated", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "_resetAdjustments", "currentTarget", "isModalOverflowing", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "allReadyOpen", "<PERSON><PERSON><PERSON>", "visibility", "blur", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "customClass", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "*", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "_disposePopper", "isWithContent", "shadowRoot", "isInTheDom", "getTitle", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "attachment", "_getAttachment", "_addAttachmentClass", "_resolvePossibleFunction", "prevHoverState", "_cleanTipClass", "<PERSON><PERSON><PERSON><PERSON>", "_sanitizeAndSetContent", "content", "templateElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textContent", "updateAttachment", "_getDelegateConfig", "_handlePopperPlacementChange", "_getBasicClassPrefix", "eventIn", "eventOut", "_fixTitle", "originalTitleType", "dataAttributes", "dataAttr", "basicClassPrefixRegex", "tabClass", "token", "tClass", "Popover", "_getContent", "SELECTOR_LINK_ITEMS", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "listGroup", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "complete", "active", "isTransitioning", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAOA,MA2BMA,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAWH,EAAQE,aAAa,QAMpC,IAAKC,IAAcA,EAASC,SAAS,OAASD,EAASE,WAAW,KAChE,OAAO,KAILF,EAASC,SAAS,OAASD,EAASE,WAAW,OACjDF,EAAY,IAAGA,EAASG,MAAM,KAAK,IAGrCL,EAAWE,GAAyB,MAAbA,EAAmBA,EAASI,OAAS,KAG9D,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,MAGHU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,MA0BjDW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MA1FL,mBA6FjBC,EAAYC,MACXA,GAAsB,iBAARA,UAIO,IAAfA,EAAIC,SACbD,EAAMA,EAAI,SAGmB,IAAjBA,EAAIE,UAGdC,EAAaH,GACbD,EAAUC,GACLA,EAAIC,OAASD,EAAI,GAAKA,EAGZ,iBAARA,GAAoBA,EAAII,OAAS,EACnCX,SAASC,cAAcM,GAGzB,KAGHK,EAAkB,CAACC,EAAeC,EAAQC,KAC9CC,OAAOC,KAAKF,GAAaG,QAAQC,IAC/B,MAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASf,EAAUe,GAAS,UArH5Cd,OADSA,EAsHsDc,GApHzD,GAAEd,EAGL,GAAGgB,SAASC,KAAKjB,GAAKkB,MAAM,eAAe,GAAGC,cALxCnB,IAAAA,EAwHX,IAAK,IAAIoB,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,UACP,GAAEhB,EAAciB,0BAA0BX,qBAA4BG,yBAAiCF,UAM1GW,EAAYxC,MACXe,EAAUf,IAAgD,IAApCA,EAAQyC,iBAAiBrB,SAIgB,YAA7DsB,iBAAiB1C,GAAS2C,iBAAiB,cAG9CC,EAAa5C,IACZA,GAAWA,EAAQkB,WAAa2B,KAAKC,gBAItC9C,EAAQ+C,UAAUC,SAAS,mBAIC,IAArBhD,EAAQiD,SACVjD,EAAQiD,SAGVjD,EAAQkD,aAAa,aAAoD,UAArClD,EAAQE,aAAa,aAG5DiD,EAAiBnD,IACrB,IAAKS,SAAS2C,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQyD,WAINN,EAAenD,EAAQyD,YAHrB,MAMLC,EAAO,OAUPC,EAAS3D,IAEbA,EAAQ4D,cAGJC,EAAY,KAChB,MAAMC,OAAEA,GAAWC,OAEnB,OAAID,IAAWrD,SAASuD,KAAKd,aAAa,qBACjCY,EAGF,MAGHG,EAA4B,GAiB5BC,EAAQ,IAAuC,QAAjCzD,SAAS2C,gBAAgBe,IAEvCC,EAAqBC,IAjBAC,IAAAA,EAAAA,EAkBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA3BQ,YAAxBnE,SAASsE,YAENd,EAA0B7C,QAC7BX,SAASuE,iBAAiB,mBAAoB,KAC5Cf,EAA0BtC,QAAQ2C,GAAYA,OAIlDL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUZ,IACU,mBAAbA,GACTA,KAIEa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA1LiCtF,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIuF,mBAAEA,EAAFC,gBAAsBA,GAAoBzB,OAAOrB,iBAAiB1C,GAEtE,MAAMyF,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBjF,MAAM,KAAK,GACnDkF,EAAkBA,EAAgBlF,MAAM,KAAK,GArFf,KAuFtBoF,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GA6KgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAtQC,gBAsQmCF,GACtDb,EAAQZ,KAGVc,EAAkBJ,iBA1QG,gBA0Q8Be,GACnDG,WAAW,KACJJ,GACHlF,EAAqBwE,IAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,IAAIC,EAAQJ,EAAKK,QAAQJ,GAGzB,IAAe,IAAXG,EACF,OAAOJ,GAAME,GAAiBC,EAAiBH,EAAKhF,OAAS,EAAI,GAGnE,MAAMsF,EAAaN,EAAKhF,OAQxB,OANAoF,GAASF,EAAgB,GAAK,EAE1BC,IACFC,GAASA,EAAQE,GAAcA,GAG1BN,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIL,EAAOE,EAAa,MCrSjDI,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAoB,4BACpBC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WASF,SAASC,EAAYzH,EAAS0H,GAC5B,OAAQA,GAAQ,GAAEA,MAAQR,OAAiBlH,EAAQkH,UAAYA,IAGjE,SAASS,EAAS3H,GAChB,MAAM0H,EAAMD,EAAYzH,GAKxB,OAHAA,EAAQkH,SAAWQ,EACnBT,EAAcS,GAAOT,EAAcS,IAAQ,GAEpCT,EAAcS,GAsCvB,SAASE,EAAYC,EAAQ9B,EAAS+B,EAAqB,MACzD,MAAMC,EAAetG,OAAOC,KAAKmG,GAEjC,IAAK,IAAIG,EAAI,EAAGC,EAAMF,EAAa3G,OAAQ4G,EAAIC,EAAKD,IAAK,CACvD,MAAME,EAAQL,EAAOE,EAAaC,IAElC,GAAIE,EAAMC,kBAAoBpC,GAAWmC,EAAMJ,qBAAuBA,EACpE,OAAOI,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBtC,EAASuC,GACnD,MAAMC,EAAgC,iBAAZxC,EACpBoC,EAAkBI,EAAaD,EAAevC,EAEpD,IAAIyC,EAAYC,EAAaJ,GAO7B,OANiBd,EAAamB,IAAIF,KAGhCA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW3I,EAASqI,EAAmBtC,EAASuC,EAAcM,GACrE,GAAiC,iBAAtBP,IAAmCrI,EAC5C,OAUF,GAPK+F,IACHA,EAAUuC,EACVA,EAAe,MAKbhB,EAAkBjF,KAAKgG,GAAoB,CAC7C,MAAMQ,EAASlE,GACN,SAAUuD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe/F,SAASkF,EAAMY,eAChH,OAAOnE,EAAG1C,KAAK+G,KAAMd,IAKvBI,EACFA,EAAeO,EAAOP,GAEtBvC,EAAU8C,EAAO9C,GAIrB,MAAOwC,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvFT,EAASF,EAAS3H,GAClBiJ,EAAWpB,EAAOW,KAAeX,EAAOW,GAAa,IACrDU,EAAatB,EAAYqB,EAAUd,EAAiBI,EAAaxC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAWN,OAASM,EAAWN,QAAUA,GAK3C,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBc,QAAQrC,EAAgB,KAC7EnC,EAAK4D,EA3Fb,SAAoCvI,EAASC,EAAU0E,GACrD,OAAO,SAASoB,EAAQmC,GACtB,MAAMkB,EAAcpJ,EAAQqJ,iBAAiBpJ,GAE7C,IAAK,IAAI+F,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOvC,WACtE,IAAK,IAAIuE,EAAIoB,EAAYhI,OAAQ4G,KAC/B,GAAIoB,EAAYpB,KAAOhC,EAOrB,OANAkC,EAAMa,eAAiB/C,EAEnBD,EAAQ6C,QACVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAMvJ,EAAU0E,GAG3CA,EAAG8E,MAAMzD,EAAQ,CAACkC,IAM/B,OAAO,MAyEPwB,CAA2B1J,EAAS+F,EAASuC,GAxGjD,SAA0BtI,EAAS2E,GACjC,OAAO,SAASoB,EAAQmC,GAOtB,OANAA,EAAMa,eAAiB/I,EAEnB+F,EAAQ6C,QACVU,EAAaC,IAAIvJ,EAASkI,EAAMsB,KAAM7E,GAGjCA,EAAG8E,MAAMzJ,EAAS,CAACkI,KAiG1ByB,CAAiB3J,EAAS+F,GAE5BpB,EAAGmD,mBAAqBS,EAAaxC,EAAU,KAC/CpB,EAAGwD,gBAAkBA,EACrBxD,EAAGiE,OAASA,EACZjE,EAAGuC,SAAWQ,EACduB,EAASvB,GAAO/C,EAEhB3E,EAAQgF,iBAAiBwD,EAAW7D,EAAI4D,GAG1C,SAASqB,EAAc5J,EAAS6H,EAAQW,EAAWzC,EAAS+B,GAC1D,MAAMnD,EAAKiD,EAAYC,EAAOW,GAAYzC,EAAS+B,GAE9CnD,IAIL3E,EAAQiG,oBAAoBuC,EAAW7D,EAAIkF,QAAQ/B,WAC5CD,EAAOW,GAAW7D,EAAGuC,WAe9B,SAASuB,EAAaP,GAGpB,OADAA,EAAQA,EAAMiB,QAAQpC,EAAgB,IAC/BI,EAAae,IAAUA,EAGhC,MAAMoB,EAAe,CACnBQ,GAAG9J,EAASkI,EAAOnC,EAASuC,GAC1BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDyB,IAAI/J,EAASkI,EAAOnC,EAASuC,GAC3BK,EAAW3I,EAASkI,EAAOnC,EAASuC,GAAc,IAGpDiB,IAAIvJ,EAASqI,EAAmBtC,EAASuC,GACvC,GAAiC,iBAAtBD,IAAmCrI,EAC5C,OAGF,MAAOuI,EAAYJ,EAAiBK,GAAaJ,EAAgBC,EAAmBtC,EAASuC,GACvF0B,EAAcxB,IAAcH,EAC5BR,EAASF,EAAS3H,GAClBiK,EAAc5B,EAAkBhI,WAAW,KAEjD,QAA+B,IAApB8H,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAoB,EAAc5J,EAAS6H,EAAQW,EAAWL,EAAiBI,EAAaxC,EAAU,MAIhFkE,GACFxI,OAAOC,KAAKmG,GAAQlG,QAAQuI,KAhDlC,SAAkClK,EAAS6H,EAAQW,EAAW2B,GAC5D,MAAMC,EAAoBvC,EAAOW,IAAc,GAE/C/G,OAAOC,KAAK0I,GAAmBzI,QAAQ0I,IACrC,GAAIA,EAAWjK,SAAS+J,GAAY,CAClC,MAAMjC,EAAQkC,EAAkBC,GAEhCT,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBA0CrEwC,CAAyBtK,EAAS6H,EAAQqC,EAAc7B,EAAkBkC,MAAM,MAIpF,MAAMH,EAAoBvC,EAAOW,IAAc,GAC/C/G,OAAOC,KAAK0I,GAAmBzI,QAAQ6I,IACrC,MAAMH,EAAaG,EAAYrB,QAAQnC,EAAe,IAEtD,IAAKgD,GAAe3B,EAAkBjI,SAASiK,GAAa,CAC1D,MAAMnC,EAAQkC,EAAkBI,GAEhCZ,EAAc5J,EAAS6H,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,wBAK7E2C,QAAQzK,EAASkI,EAAOwC,GACtB,GAAqB,iBAAVxC,IAAuBlI,EAChC,OAAO,KAGT,MAAMuE,EAAIV,IACJ2E,EAAYC,EAAaP,GACzB8B,EAAc9B,IAAUM,EACxBmC,EAAWpD,EAAamB,IAAIF,GAElC,IAAIoC,EACAC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIhB,GAAezF,IACjBqG,EAAcrG,EAAEzD,MAAMoH,EAAOwC,GAE7BnG,EAAEvE,GAASyK,QAAQG,GACnBC,GAAWD,EAAYK,uBACvBH,GAAkBF,EAAYM,gCAC9BH,EAAmBH,EAAYO,sBAG7BR,GACFK,EAAMvK,SAAS2K,YAAY,cAC3BJ,EAAIK,UAAU7C,EAAWqC,GAAS,IAElCG,EAAM,IAAIM,YAAYpD,EAAO,CAC3B2C,QAAAA,EACAU,YAAY,SAKI,IAATb,GACTjJ,OAAOC,KAAKgJ,GAAM/I,QAAQ6J,IACxB/J,OAAOgK,eAAeT,EAAKQ,EAAK,CAC9BE,IAAG,IACMhB,EAAKc,OAMhBT,GACFC,EAAIW,iBAGFb,GACF9K,EAAQa,cAAcmK,GAGpBA,EAAID,uBAA2C,IAAhBH,GACjCA,EAAYe,iBAGPX,IC1ULY,EAAa,IAAIC,IAEvB,IAAAC,EAAe,CACbC,IAAI/L,EAASwL,EAAKQ,GACXJ,EAAWlD,IAAI1I,IAClB4L,EAAWG,IAAI/L,EAAS,IAAI6L,KAG9B,MAAMI,EAAcL,EAAWF,IAAI1L,GAI9BiM,EAAYvD,IAAI8C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAYvK,QAAQ,QAOhIgK,IAAG,CAAC1L,EAASwL,IACPI,EAAWlD,IAAI1I,IACV4L,EAAWF,IAAI1L,GAAS0L,IAAIF,IAG9B,KAGTe,OAAOvM,EAASwL,GACd,IAAKI,EAAWlD,IAAI1I,GAClB,OAGF,MAAMiM,EAAcL,EAAWF,IAAI1L,GAEnCiM,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAOxM,KC/BxB,MAAMyM,EACJC,YAAY1M,IACVA,EAAUmB,EAAWnB,MAMrBgJ,KAAK2D,SAAW3M,EAChB8L,EAAKC,IAAI/C,KAAK2D,SAAU3D,KAAK0D,YAAYE,SAAU5D,OAGrD6D,UACEf,EAAKS,OAAOvD,KAAK2D,SAAU3D,KAAK0D,YAAYE,UAC5CtD,EAAaC,IAAIP,KAAK2D,SAAU3D,KAAK0D,YAAYI,WAEjDrL,OAAOsL,oBAAoB/D,MAAMrH,QAAQqL,IACvChE,KAAKgE,GAAgB,OAIzBC,eAAe3I,EAAUtE,EAASkN,GAAa,GAC7C/H,EAAuBb,EAAUtE,EAASkN,GAK1BC,mBAACnN,GACjB,OAAO8L,EAAKJ,IAAIvK,EAAWnB,GAAUgJ,KAAK4D,UAGlBO,2BAACnN,EAASuB,EAAS,IAC3C,OAAOyH,KAAKoE,YAAYpN,IAAY,IAAIgJ,KAAKhJ,EAA2B,iBAAXuB,EAAsBA,EAAS,MAG5E8L,qBAChB,MAtCY,QAyCC5I,kBACb,MAAM,IAAI6I,MAAM,uEAGCV,sBACjB,MAAQ,MAAK5D,KAAKvE,KAGAqI,uBAClB,MAAQ,IAAG9D,KAAK4D,UC5DpB,MAAMW,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUV,UACvCtI,EAAOgJ,EAAU/I,KAEvB6E,EAAaQ,GAAGrJ,SAAUiN,EAAa,qBAAoBlJ,OAAU,SAAU0D,GAK7E,GAJI,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGF,MAAMhD,EAASrF,EAAuBqI,OAASA,KAAK4E,QAAS,IAAGpJ,GAC/CgJ,EAAUK,oBAAoB7H,GAGtCyH,SCMb,MAAMK,UAAcrB,EAGHhI,kBACb,MAnBS,QAwBXsJ,QAGE,GAFmBzE,EAAamB,QAAQzB,KAAK2D,SArB5B,kBAuBF5B,iBACb,OAGF/B,KAAK2D,SAAS5J,UAAUwJ,OAxBJ,QA0BpB,MAAMW,EAAalE,KAAK2D,SAAS5J,UAAUC,SA3BvB,QA4BpBgG,KAAKiE,eAAe,IAAMjE,KAAKgF,kBAAmBhF,KAAK2D,SAAUO,GAInEc,kBACEhF,KAAK2D,SAASJ,SACdjD,EAAamB,QAAQzB,KAAK2D,SAnCR,mBAoClB3D,KAAK6D,UAKeM,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoB7E,MAEvC,GAAsB,iBAAXzH,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBuE,EAAqBO,EAAO,SAS5B1J,EAAmB0J,GC9DnB,MAAMM,UAAe3B,EAGJhI,kBACb,MArBS,SA0BX4J,SAEErF,KAAK2D,SAAS2B,aAAa,eAAgBtF,KAAK2D,SAAS5J,UAAUsL,OAvB7C,WA4BFlB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOE,EAAOP,oBAAoB7E,MAEzB,WAAXzH,GACF2M,EAAK3M,SChDb,SAASgN,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQ9I,OAAO8I,GAAKxM,WACf0D,OAAO8I,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASC,EAAiBjD,GACxB,OAAOA,EAAIrC,QAAQ,SAAUuF,GAAQ,IAAGA,EAAIvM,eDuC9CmH,EAAaQ,GAAGrJ,SAzCc,2BAFD,4BA2CyCyH,IACpEA,EAAMyD,iBAEN,MAAMgD,EAASzG,EAAMlC,OAAO4H,QA9CD,6BA+CdQ,EAAOP,oBAAoBc,GAEnCN,WAUPjK,EAAmBgK,GCpDnB,MAAMQ,EAAc,CAClBC,iBAAiB7O,EAASwL,EAAK1J,GAC7B9B,EAAQsO,aAAc,WAAUG,EAAiBjD,GAAQ1J,IAG3DgN,oBAAoB9O,EAASwL,GAC3BxL,EAAQ+O,gBAAiB,WAAUN,EAAiBjD,KAGtDwD,kBAAkBhP,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMiP,EAAa,GAUnB,OARAxN,OAAOC,KAAK1B,EAAQkP,SACjBC,OAAO3D,GAAOA,EAAInL,WAAW,OAC7BsB,QAAQ6J,IACP,IAAI4D,EAAU5D,EAAIrC,QAAQ,MAAO,IACjCiG,EAAUA,EAAQC,OAAO,GAAGlN,cAAgBiN,EAAQ7E,MAAM,EAAG6E,EAAQhO,QACrE6N,EAAWG,GAAWb,EAAcvO,EAAQkP,QAAQ1D,MAGjDyD,GAGTK,iBAAgB,CAACtP,EAASwL,IACjB+C,EAAcvO,EAAQE,aAAc,WAAUuO,EAAiBjD,KAGxE+D,OAAOvP,GACL,MAAMwP,EAAOxP,EAAQyP,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM3L,OAAO4L,YACvBC,KAAMJ,EAAKI,KAAO7L,OAAO8L,cAI7BC,SAAS9P,IACA,CACL0P,IAAK1P,EAAQ+P,UACbH,KAAM5P,EAAQgQ,cCzDdC,EAAiB,CACrBC,KAAI,CAACjQ,EAAUD,EAAUS,SAAS2C,kBACzB,GAAG+M,UAAUC,QAAQC,UAAUhH,iBAAiBpH,KAAKjC,EAASC,IAGvEqQ,QAAO,CAACrQ,EAAUD,EAAUS,SAAS2C,kBAC5BgN,QAAQC,UAAU3P,cAAcuB,KAAKjC,EAASC,GAGvDsQ,SAAQ,CAACvQ,EAASC,IACT,GAAGkQ,UAAUnQ,EAAQuQ,UACzBpB,OAAOqB,GAASA,EAAMC,QAAQxQ,IAGnCyQ,QAAQ1Q,EAASC,GACf,MAAMyQ,EAAU,GAEhB,IAAIC,EAAW3Q,EAAQyD,WAEvB,KAAOkN,GAAYA,EAASzP,WAAa2B,KAAKC,cArBhC,IAqBgD6N,EAASzP,UACjEyP,EAASF,QAAQxQ,IACnByQ,EAAQzL,KAAK0L,GAGfA,EAAWA,EAASlN,WAGtB,OAAOiN,GAGTE,KAAK5Q,EAASC,GACZ,IAAI4Q,EAAW7Q,EAAQ8Q,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQxQ,GACnB,MAAO,CAAC4Q,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAK/Q,EAASC,GACZ,IAAI8Q,EAAO/Q,EAAQgR,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQxQ,GACf,MAAO,CAAC8Q,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,IAGTC,kBAAkBjR,GAChB,MAAMkR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,IAAIlR,GAAeA,EAAF,yBAAmCmR,KAAK,MAE3D,OAAOpI,KAAKkH,KAAKgB,EAAYlR,GAASmP,OAAOkC,IAAOzO,EAAWyO,IAAO7O,EAAU6O,MCjD9EC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAElBC,EAAmB,CACvBC,UAAkBF,EAClBG,WAAmBJ,GA4CrB,MAAMK,UAAiB5F,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKuJ,OAAS,KACdvJ,KAAKwJ,UAAY,KACjBxJ,KAAKyJ,eAAiB,KACtBzJ,KAAK0J,WAAY,EACjB1J,KAAK2J,YAAa,EAClB3J,KAAK4J,aAAe,KACpB5J,KAAK6J,YAAc,EACnB7J,KAAK8J,YAAc,EAEnB9J,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKiK,mBAAqBhD,EAAeK,QA3BjB,uBA2B8CtH,KAAK2D,UAC3E3D,KAAKkK,gBAAkB,iBAAkBzS,SAAS2C,iBAAmB+P,UAAUC,eAAiB,EAChGpK,KAAKqK,cAAgBxJ,QAAQ9F,OAAOuP,cAEpCtK,KAAKuK,qBAKWjC,qBAChB,OAAOA,EAGM7M,kBACb,MA3GS,WAgHXsM,OACE/H,KAAKwK,OAAO1B,GAGd2B,mBAGOhT,SAASiT,QAAUlR,EAAUwG,KAAK2D,WACrC3D,KAAK+H,OAITH,OACE5H,KAAKwK,OAAOzB,GAGdL,MAAMxJ,GACCA,IACHc,KAAK0J,WAAY,GAGfzC,EAAeK,QApEI,2CAoEwBtH,KAAK2D,YAClD/L,EAAqBoI,KAAK2D,UAC1B3D,KAAK2K,OAAM,IAGbC,cAAc5K,KAAKwJ,WACnBxJ,KAAKwJ,UAAY,KAGnBmB,MAAMzL,GACCA,IACHc,KAAK0J,WAAY,GAGf1J,KAAKwJ,YACPoB,cAAc5K,KAAKwJ,WACnBxJ,KAAKwJ,UAAY,MAGfxJ,KAAK+J,SAAW/J,KAAK+J,QAAQxB,WAAavI,KAAK0J,YACjD1J,KAAK6K,kBAEL7K,KAAKwJ,UAAYsB,aACdrT,SAASsT,gBAAkB/K,KAAKyK,gBAAkBzK,KAAK+H,MAAMiD,KAAKhL,MACnEA,KAAK+J,QAAQxB,WAKnB0C,GAAGzN,GACDwC,KAAKyJ,eAAiBxC,EAAeK,QArGZ,wBAqG0CtH,KAAK2D,UACxE,MAAMuH,EAAclL,KAAKmL,cAAcnL,KAAKyJ,gBAE5C,GAAIjM,EAAQwC,KAAKuJ,OAAOnR,OAAS,GAAKoF,EAAQ,EAC5C,OAGF,GAAIwC,KAAK2J,WAEP,YADArJ,EAAaS,IAAIf,KAAK2D,SApIR,mBAoI8B,IAAM3D,KAAKiL,GAAGzN,IAI5D,GAAI0N,IAAgB1N,EAGlB,OAFAwC,KAAK0I,aACL1I,KAAK2K,QAIP,MAAMS,EAAQ5N,EAAQ0N,EACpBpC,EACAC,EAEF/I,KAAKwK,OAAOY,EAAOpL,KAAKuJ,OAAO/L,IAKjCwM,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,KACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EApMS,WAoMaE,EAAQsQ,GACvBtQ,EAGT8S,eACE,MAAMC,EAAY3N,KAAK4N,IAAIvL,KAAK8J,aAEhC,GAAIwB,GAnMgB,GAoMlB,OAGF,MAAME,EAAYF,EAAYtL,KAAK8J,YAEnC9J,KAAK8J,YAAc,EAEd0B,GAILxL,KAAKwK,OAAOgB,EAAY,EAAIvC,EAAkBD,GAGhDuB,qBACMvK,KAAK+J,QAAQvB,UACflI,EAAaQ,GAAGd,KAAK2D,SApLJ,sBAoL6BzE,GAASc,KAAKyL,SAASvM,IAG5C,UAAvBc,KAAK+J,QAAQrB,QACfpI,EAAaQ,GAAGd,KAAK2D,SAvLD,yBAuL6BzE,GAASc,KAAK0I,MAAMxJ,IACrEoB,EAAaQ,GAAGd,KAAK2D,SAvLD,yBAuL6BzE,GAASc,KAAK2K,MAAMzL,KAGnEc,KAAK+J,QAAQnB,OAAS5I,KAAKkK,iBAC7BlK,KAAK0L,0BAITA,0BACE,MAAMC,EAAqBzM,GAClBc,KAAKqK,gBAnKO,QAoKhBnL,EAAM0M,aArKY,UAqKwB1M,EAAM0M,aAG/CC,EAAQ3M,IACRyM,EAAmBzM,GACrBc,KAAK6J,YAAc3K,EAAM4M,QACf9L,KAAKqK,gBACfrK,KAAK6J,YAAc3K,EAAM6M,QAAQ,GAAGD,UAIlCE,EAAO9M,IAEXc,KAAK8J,YAAc5K,EAAM6M,SAAW7M,EAAM6M,QAAQ3T,OAAS,EACzD,EACA8G,EAAM6M,QAAQ,GAAGD,QAAU9L,KAAK6J,aAG9BoC,EAAM/M,IACNyM,EAAmBzM,KACrBc,KAAK8J,YAAc5K,EAAM4M,QAAU9L,KAAK6J,aAG1C7J,KAAKqL,eACsB,UAAvBrL,KAAK+J,QAAQrB,QASf1I,KAAK0I,QACD1I,KAAK4J,cACPsC,aAAalM,KAAK4J,cAGpB5J,KAAK4J,aAAe1M,WAAWgC,GAASc,KAAK2K,MAAMzL,GA3Q5B,IA2Q6Dc,KAAK+J,QAAQxB,YAIrGtB,EAAeC,KAtNO,qBAsNiBlH,KAAK2D,UAAUhL,QAAQwT,IAC5D7L,EAAaQ,GAAGqL,EAvOI,wBAuOuBC,GAAKA,EAAEzJ,oBAGhD3C,KAAKqK,eACP/J,EAAaQ,GAAGd,KAAK2D,SA7OA,0BA6O6BzE,GAAS2M,EAAM3M,IACjEoB,EAAaQ,GAAGd,KAAK2D,SA7OF,wBA6O6BzE,GAAS+M,EAAI/M,IAE7Dc,KAAK2D,SAAS5J,UAAUsS,IAnOG,mBAqO3B/L,EAAaQ,GAAGd,KAAK2D,SArPD,yBAqP6BzE,GAAS2M,EAAM3M,IAChEoB,EAAaQ,GAAGd,KAAK2D,SArPF,wBAqP6BzE,GAAS8M,EAAK9M,IAC9DoB,EAAaQ,GAAGd,KAAK2D,SArPH,uBAqP6BzE,GAAS+M,EAAI/M,KAIhEuM,SAASvM,GACP,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SACtC,OAGF,MAAM6G,EAAYtC,EAAiBhK,EAAMsD,KACrCgJ,IACFtM,EAAMyD,iBACN3C,KAAKwK,OAAOgB,IAIhBL,cAAcnU,GAKZ,OAJAgJ,KAAKuJ,OAASvS,GAAWA,EAAQyD,WAC/BwM,EAAeC,KArPC,iBAqPmBlQ,EAAQyD,YAC3C,GAEKuF,KAAKuJ,OAAO9L,QAAQzG,GAG7BsV,gBAAgBlB,EAAO/N,GACrB,MAAMkP,EAASnB,IAAUtC,EACzB,OAAO3L,EAAqB6C,KAAKuJ,OAAQlM,EAAekP,EAAQvM,KAAK+J,QAAQpB,MAG/E6D,mBAAmB1M,EAAe2M,GAChC,MAAMC,EAAc1M,KAAKmL,cAAcrL,GACjC6M,EAAY3M,KAAKmL,cAAclE,EAAeK,QAnQ3B,wBAmQyDtH,KAAK2D,WAEvF,OAAOrD,EAAamB,QAAQzB,KAAK2D,SA7RhB,oBA6RuC,CACtD7D,cAAAA,EACA0L,UAAWiB,EACXnJ,KAAMqJ,EACN1B,GAAIyB,IAIRE,2BAA2B5V,GACzB,GAAIgJ,KAAKiK,mBAAoB,CAC3B,MAAM4C,EAAkB5F,EAAeK,QAhRrB,UAgR8CtH,KAAKiK,oBAErE4C,EAAgB9S,UAAUwJ,OA1RN,UA2RpBsJ,EAAgB9G,gBAAgB,gBAEhC,MAAM+G,EAAa7F,EAAeC,KA/Qb,mBA+QsClH,KAAKiK,oBAEhE,IAAK,IAAIjL,EAAI,EAAGA,EAAI8N,EAAW1U,OAAQ4G,IACrC,GAAItC,OAAOqQ,SAASD,EAAW9N,GAAG9H,aAAa,oBAAqB,MAAQ8I,KAAKmL,cAAcnU,GAAU,CACvG8V,EAAW9N,GAAGjF,UAAUsS,IAjSR,UAkShBS,EAAW9N,GAAGsG,aAAa,eAAgB,QAC3C,QAMRuF,kBACE,MAAM7T,EAAUgJ,KAAKyJ,gBAAkBxC,EAAeK,QAjS7B,wBAiS2DtH,KAAK2D,UAEzF,IAAK3M,EACH,OAGF,MAAMgW,EAAkBtQ,OAAOqQ,SAAS/V,EAAQE,aAAa,oBAAqB,IAE9E8V,GACFhN,KAAK+J,QAAQkD,gBAAkBjN,KAAK+J,QAAQkD,iBAAmBjN,KAAK+J,QAAQxB,SAC5EvI,KAAK+J,QAAQxB,SAAWyE,GAExBhN,KAAK+J,QAAQxB,SAAWvI,KAAK+J,QAAQkD,iBAAmBjN,KAAK+J,QAAQxB,SAIzEiC,OAAO0C,EAAkBlW,GACvB,MAAMoU,EAAQpL,KAAKmN,kBAAkBD,GAC/B7P,EAAgB4J,EAAeK,QAnTZ,wBAmT0CtH,KAAK2D,UAClEyJ,EAAqBpN,KAAKmL,cAAc9N,GACxCgQ,EAAcrW,GAAWgJ,KAAKsM,gBAAgBlB,EAAO/N,GAErDiQ,EAAmBtN,KAAKmL,cAAckC,GACtCE,EAAY1M,QAAQb,KAAKwJ,WAEzB+C,EAASnB,IAAUtC,EACnB0E,EAAuBjB,EAjUR,sBADF,oBAmUbkB,EAAiBlB,EAjUH,qBACA,qBAiUdE,EAAqBzM,KAAK0N,kBAAkBtC,GAElD,GAAIiC,GAAeA,EAAYtT,UAAUC,SAxUnB,UA0UpB,YADAgG,KAAK2J,YAAa,GAIpB,GAAI3J,KAAK2J,WACP,OAIF,GADmB3J,KAAKwM,mBAAmBa,EAAaZ,GACzC1K,iBACb,OAGF,IAAK1E,IAAkBgQ,EAErB,OAGFrN,KAAK2J,YAAa,EAEd4D,GACFvN,KAAK0I,QAGP1I,KAAK4M,2BAA2BS,GAChCrN,KAAKyJ,eAAiB4D,EAEtB,MAAMM,EAAmB,KACvBrN,EAAamB,QAAQzB,KAAK2D,SAnXZ,mBAmXkC,CAC9C7D,cAAeuN,EACf7B,UAAWiB,EACXnJ,KAAM8J,EACNnC,GAAIqC,KAIR,GAAItN,KAAK2D,SAAS5J,UAAUC,SA5WP,SA4WmC,CACtDqT,EAAYtT,UAAUsS,IAAIoB,GAE1B9S,EAAO0S,GAEPhQ,EAActD,UAAUsS,IAAImB,GAC5BH,EAAYtT,UAAUsS,IAAImB,GAE1B,MAAMI,EAAmB,KACvBP,EAAYtT,UAAUwJ,OAAOiK,EAAsBC,GACnDJ,EAAYtT,UAAUsS,IAvXJ,UAyXlBhP,EAActD,UAAUwJ,OAzXN,SAyXgCkK,EAAgBD,GAElExN,KAAK2J,YAAa,EAElBzM,WAAWyQ,EAAkB,IAG/B3N,KAAKiE,eAAe2J,EAAkBvQ,GAAe,QAErDA,EAActD,UAAUwJ,OAlYJ,UAmYpB8J,EAAYtT,UAAUsS,IAnYF,UAqYpBrM,KAAK2J,YAAa,EAClBgE,IAGEJ,GACFvN,KAAK2K,QAITwC,kBAAkB3B,GAChB,MAAK,CAACvC,EAAiBD,GAAgB5R,SAASoU,GAI5CtQ,IACKsQ,IAAcxC,EAAiBD,EAAaD,EAG9C0C,IAAcxC,EAAiBF,EAAaC,EAP1CyC,EAUXkC,kBAAkBtC,GAChB,MAAK,CAACtC,EAAYC,GAAY3R,SAASgU,GAInClQ,IACKkQ,IAAUrC,EAAaC,EAAiBC,EAG1CmC,IAAUrC,EAAaE,EAAkBD,EAPvCoC,EAYajH,yBAACnN,EAASuB,GAChC,MAAM2M,EAAOmE,EAASxE,oBAAoB7N,EAASuB,GAEnD,IAAIwR,QAAEA,GAAY7E,EACI,iBAAX3M,IACTwR,EAAU,IACLA,KACAxR,IAIP,MAAMsV,EAA2B,iBAAXtV,EAAsBA,EAASwR,EAAQtB,MAE7D,GAAsB,iBAAXlQ,EACT2M,EAAK+F,GAAG1S,QACH,GAAsB,iBAAXsV,EAAqB,CACrC,QAA4B,IAAjB3I,EAAK2I,GACd,MAAM,IAAIvU,UAAW,oBAAmBuU,MAG1C3I,EAAK2I,UACI9D,EAAQxB,UAAYwB,EAAQ+D,OACrC5I,EAAKwD,QACLxD,EAAKyF,SAIaxG,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACfoE,EAAS0E,kBAAkB/N,KAAMzH,MAIX4L,2BAACjF,GACzB,MAAMlC,EAASrF,EAAuBqI,MAEtC,IAAKhD,IAAWA,EAAOjD,UAAUC,SA7cT,YA8ctB,OAGF,MAAMzB,EAAS,IACVqN,EAAYI,kBAAkBhJ,MAC9B4I,EAAYI,kBAAkBhG,OAE7BgO,EAAahO,KAAK9I,aAAa,oBAEjC8W,IACFzV,EAAOgQ,UAAW,GAGpBc,EAAS0E,kBAAkB/Q,EAAQzE,GAE/ByV,GACF3E,EAASjF,YAAYpH,GAAQiO,GAAG+C,GAGlC9O,EAAMyD,kBAUVrC,EAAaQ,GAAGrJ,SA7ec,6BAkBF,sCA2dyC4R,EAAS4E,qBAE9E3N,EAAaQ,GAAG/F,OAhfa,4BAgfgB,KAC3C,MAAMmT,EAAYjH,EAAeC,KA7dR,6BA+dzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMiP,EAAU9V,OAAQ4G,EAAIC,EAAKD,IAC/CqK,EAAS0E,kBAAkBG,EAAUlP,GAAIqK,EAASjF,YAAY8J,EAAUlP,OAW5E5D,EAAmBiO,GCjjBnB,MAKMf,EAAU,CACdjD,QAAQ,EACR8I,OAAQ,MAGJtF,GAAc,CAClBxD,OAAQ,UACR8I,OAAQ,kBA2BV,MAAMC,WAAiB3K,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKqO,kBAAmB,EACxBrO,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKsO,cAAgB,GAErB,MAAMC,EAAatH,EAAeC,KAhBT,+BAkBzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMsP,EAAWnW,OAAQ4G,EAAIC,EAAKD,IAAK,CACrD,MAAMwP,EAAOD,EAAWvP,GAClB/H,EAAWO,EAAuBgX,GAClCC,EAAgBxH,EAAeC,KAAKjQ,GACvCkP,OAAOuI,GAAaA,IAAc1O,KAAK2D,UAEzB,OAAb1M,GAAqBwX,EAAcrW,SACrC4H,KAAK2O,UAAY1X,EACjB+I,KAAKsO,cAAcrS,KAAKuS,IAI5BxO,KAAK4O,sBAEA5O,KAAK+J,QAAQoE,QAChBnO,KAAK6O,0BAA0B7O,KAAKsO,cAAetO,KAAK8O,YAGtD9O,KAAK+J,QAAQ1E,QACfrF,KAAKqF,SAMSiD,qBAChB,OAAOA,EAGM7M,kBACb,MA/ES,WAoFX4J,SACMrF,KAAK8O,WACP9O,KAAK+O,OAEL/O,KAAKgP,OAITA,OACE,GAAIhP,KAAKqO,kBAAoBrO,KAAK8O,WAChC,OAGF,IACIG,EADAC,EAAU,GAGd,GAAIlP,KAAK+J,QAAQoE,OAAQ,CACvB,MAAM5G,EAAWN,EAAeC,KAAM,sBAAkDlH,KAAK+J,QAAQoE,QACrGe,EAAUjI,EAAeC,KAxEN,uCAwE6BlH,KAAK+J,QAAQoE,QAAQhI,OAAOqI,IAASjH,EAASnQ,SAASoX,IAGzG,MAAMW,EAAYlI,EAAeK,QAAQtH,KAAK2O,WAC9C,GAAIO,EAAQ9W,OAAQ,CAClB,MAAMgX,EAAiBF,EAAQhI,KAAKsH,GAAQW,IAAcX,GAG1D,GAFAS,EAAcG,EAAiBhB,GAAShK,YAAYgL,GAAkB,KAElEH,GAAeA,EAAYZ,iBAC7B,OAKJ,GADmB/N,EAAamB,QAAQzB,KAAK2D,SApG7B,oBAqGD5B,iBACb,OAGFmN,EAAQvW,QAAQ0W,IACVF,IAAcE,GAChBjB,GAASvJ,oBAAoBwK,EAAY,CAAEhK,QAAQ,IAAS0J,OAGzDE,GACHnM,EAAKC,IAAIsM,EA7HA,cA6HsB,QAInC,MAAMC,EAAYtP,KAAKuP,gBAEvBvP,KAAK2D,SAAS5J,UAAUwJ,OA9GA,YA+GxBvD,KAAK2D,SAAS5J,UAAUsS,IA9GE,cAgH1BrM,KAAK2D,SAAS6L,MAAMF,GAAa,EAEjCtP,KAAK6O,0BAA0B7O,KAAKsO,eAAe,GACnDtO,KAAKqO,kBAAmB,EAExB,MAYMoB,EAAc,UADSH,EAAU,GAAG/V,cAAgB+V,EAAU/N,MAAM,IAG1EvB,KAAKiE,eAdY,KACfjE,KAAKqO,kBAAmB,EAExBrO,KAAK2D,SAAS5J,UAAUwJ,OAxHA,cAyHxBvD,KAAK2D,SAAS5J,UAAUsS,IA1HF,WADJ,QA6HlBrM,KAAK2D,SAAS6L,MAAMF,GAAa,GAEjChP,EAAamB,QAAQzB,KAAK2D,SApIX,sBA0Ia3D,KAAK2D,UAAU,GAC7C3D,KAAK2D,SAAS6L,MAAMF,GAAgBtP,KAAK2D,SAAS8L,GAAhB,KAGpCV,OACE,GAAI/O,KAAKqO,mBAAqBrO,KAAK8O,WACjC,OAIF,GADmBxO,EAAamB,QAAQzB,KAAK2D,SAlJ7B,oBAmJD5B,iBACb,OAGF,MAAMuN,EAAYtP,KAAKuP,gBAEvBvP,KAAK2D,SAAS6L,MAAMF,GAAgBtP,KAAK2D,SAAS8C,wBAAwB6I,GAAxC,KAElC3U,EAAOqF,KAAK2D,UAEZ3D,KAAK2D,SAAS5J,UAAUsS,IAvJE,cAwJ1BrM,KAAK2D,SAAS5J,UAAUwJ,OAzJA,WADJ,QA4JpB,MAAMmM,EAAqB1P,KAAKsO,cAAclW,OAC9C,IAAK,IAAI4G,EAAI,EAAGA,EAAI0Q,EAAoB1Q,IAAK,CAC3C,MAAMyC,EAAUzB,KAAKsO,cAActP,GAC7BwP,EAAO7W,EAAuB8J,GAEhC+M,IAASxO,KAAK8O,SAASN,IACzBxO,KAAK6O,0BAA0B,CAACpN,IAAU,GAI9CzB,KAAKqO,kBAAmB,EASxBrO,KAAK2D,SAAS6L,MAAMF,GAAa,GAEjCtP,KAAKiE,eATY,KACfjE,KAAKqO,kBAAmB,EACxBrO,KAAK2D,SAAS5J,UAAUwJ,OAxKA,cAyKxBvD,KAAK2D,SAAS5J,UAAUsS,IA1KF,YA2KtB/L,EAAamB,QAAQzB,KAAK2D,SA/KV,uBAoLY3D,KAAK2D,UAAU,GAG/CmL,SAAS9X,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SArLL,QA0LtBgQ,WAAWzR,GAST,OARAA,EAAS,IACJ+P,KACA1C,EAAYI,kBAAkBhG,KAAK2D,aACnCpL,IAEE8M,OAASxE,QAAQtI,EAAO8M,QAC/B9M,EAAO4V,OAAShW,EAAWI,EAAO4V,QAClC9V,EAvNS,WAuNaE,EAAQsQ,IACvBtQ,EAGTgX,gBACE,OAAOvP,KAAK2D,SAAS5J,UAAUC,SAnML,uBAEhB,QACC,SAmMb4U,sBACE,IAAK5O,KAAK+J,QAAQoE,OAChB,OAGF,MAAM5G,EAAWN,EAAeC,KAAM,sBAAkDlH,KAAK+J,QAAQoE,QACrGlH,EAAeC,KAtMU,8BAsMiBlH,KAAK+J,QAAQoE,QAAQhI,OAAOqI,IAASjH,EAASnQ,SAASoX,IAC9F7V,QAAQ3B,IACP,MAAM2Y,EAAWhY,EAAuBX,GAEpC2Y,GACF3P,KAAK6O,0BAA0B,CAAC7X,GAAUgJ,KAAK8O,SAASa,MAKhEd,0BAA0Be,EAAcC,GACjCD,EAAaxX,QAIlBwX,EAAajX,QAAQ6V,IACfqB,EACFrB,EAAKzU,UAAUwJ,OA9NM,aAgOrBiL,EAAKzU,UAAUsS,IAhOM,aAmOvBmC,EAAKlJ,aAAa,gBAAiBuK,KAMjB1L,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAM8E,EAAU,GACM,iBAAXxR,GAAuB,YAAYc,KAAKd,KACjDwR,EAAQ1E,QAAS,GAGnB,MAAMH,EAAOkJ,GAASvJ,oBAAoB7E,KAAM+J,GAEhD,GAAsB,iBAAXxR,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAxQc,6BAYD,+BA4PyC,SAAUyH,IAEjD,MAAzBA,EAAMlC,OAAO2H,SAAoBzF,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAe4E,UAChFzF,EAAMyD,iBAGR,MAAM1L,EAAWO,EAAuBwI,MACfiH,EAAeC,KAAKjQ,GAE5B0B,QAAQ3B,IACvBoX,GAASvJ,oBAAoB7N,EAAS,CAAEqO,QAAQ,IAASA,cAW7DjK,EAAmBgT,IC3UZ,IAAI1H,GAAM,MACNoJ,GAAS,SACTC,GAAQ,QACRnJ,GAAO,OAEPoJ,GAAiB,CAACtJ,GAAKoJ,GAAQC,GAAOnJ,IAEtCqF,GAAM,MAKNgE,GAAmCD,GAAeE,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIhJ,OAAO,CAACiJ,EAAAA,SAAyBA,EAAY,IAAMnE,OAC7D,IACQoE,GAA0B,GAAGlJ,OAAO6I,GAAgB,CAX7C,SAWqDE,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIhJ,OAAO,CAACiJ,EAAWA,EAAAA,SAAyBA,EAAY,IAAMnE,OACxE,IAaQqE,GAAiB,CAXJ,aACN,OACK,YAEC,aACN,OACK,YAEE,cACN,QACK,cC7BT,SAASC,GAAYvZ,GAClC,OAAOA,GAAWA,EAAQwZ,UAAY,IAAIrX,cAAgB,KCD7C,SAASsX,GAAUC,GAChC,GAAY,MAARA,EACF,OAAO3V,OAGT,GAAwB,oBAApB2V,EAAK1X,WAAkC,CACzC,IAAI2X,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB7V,OAG/D,OAAO2V,ECRT,SAAS3Y,GAAU2Y,GAEjB,OAAOA,aADUD,GAAUC,GAAMtJ,SACIsJ,aAAgBtJ,QAGvD,SAASyJ,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAflW,aAKJkW,aADUD,GAAUC,GAAMlW,YACIkW,aAAgBlW,YCyDvD,IAAAwW,GAAe,CACbxV,KAAM,cACNyV,SAAS,EACTC,MAAO,QACPvV,GA5EF,SAAqBwV,GACnB,IAAIC,EAAQD,EAAKC,MACjB3Y,OAAOC,KAAK0Y,EAAMC,UAAU1Y,SAAQ,SAAU6C,GAC5C,IAAIgU,EAAQ4B,EAAME,OAAO9V,IAAS,GAC9ByK,EAAamL,EAAMnL,WAAWzK,IAAS,GACvCxE,EAAUoa,EAAMC,SAAS7V,GAExBqV,GAAc7Z,IAAauZ,GAAYvZ,KAO5CyB,OAAO8Y,OAAOva,EAAQwY,MAAOA,GAC7B/W,OAAOC,KAAKuN,GAAYtN,SAAQ,SAAU6C,GACxC,IAAI1C,EAAQmN,EAAWzK,IAET,IAAV1C,EACF9B,EAAQ+O,gBAAgBvK,GAExBxE,EAAQsO,aAAa9J,GAAgB,IAAV1C,EAAiB,GAAKA,WAwDvD0Y,OAlDF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MACdM,EAAgB,CAClBC,OAAQ,CACN7K,SAAUsK,EAAMQ,QAAQC,SACxBjL,KAAM,IACNF,IAAK,IACLoL,OAAQ,KAEVC,MAAO,CACLjL,SAAU,YAEZkL,UAAW,IASb,OAPAvZ,OAAO8Y,OAAOH,EAAMC,SAASM,OAAOnC,MAAOkC,EAAcC,QACzDP,EAAME,OAASI,EAEXN,EAAMC,SAASU,OACjBtZ,OAAO8Y,OAAOH,EAAMC,SAASU,MAAMvC,MAAOkC,EAAcK,OAGnD,WACLtZ,OAAOC,KAAK0Y,EAAMC,UAAU1Y,SAAQ,SAAU6C,GAC5C,IAAIxE,EAAUoa,EAAMC,SAAS7V,GACzByK,EAAamL,EAAMnL,WAAWzK,IAAS,GAGvCgU,EAFkB/W,OAAOC,KAAK0Y,EAAME,OAAOW,eAAezW,GAAQ4V,EAAME,OAAO9V,GAAQkW,EAAclW,IAE7E0U,QAAO,SAAUV,EAAO5W,GAElD,OADA4W,EAAM5W,GAAY,GACX4W,IACN,IAEEqB,GAAc7Z,IAAauZ,GAAYvZ,KAI5CyB,OAAO8Y,OAAOva,EAAQwY,MAAOA,GAC7B/W,OAAOC,KAAKuN,GAAYtN,SAAQ,SAAUuZ,GACxClb,EAAQ+O,gBAAgBmM,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBhC,GACvC,OAAOA,EAAU9Y,MAAM,KAAK,GCD9B,IAAI+a,GAAQ1U,KAAK0U,MACF,SAAS5L,GAAsBzP,EAASsb,QAChC,IAAjBA,IACFA,GAAe,GAGjB,IAAI9L,EAAOxP,EAAQyP,wBACf8L,EAAS,EACTC,EAAS,EAEb,GAAI3B,GAAc7Z,IAAYsb,EAAc,CAC1C,IAAI1X,EAAe5D,EAAQ4D,aACvB6X,EAAczb,EAAQyb,YAGtBA,EAAc,IAChBF,EAAS/L,EAAKkM,MAAQD,GAAe,GAGnC7X,EAAe,IACjB4X,EAAShM,EAAKmM,OAAS/X,GAAgB,GAI3C,MAAO,CACL8X,MAAOL,GAAM7L,EAAKkM,MAAQH,GAC1BI,OAAQN,GAAM7L,EAAKmM,OAASH,GAC5B9L,IAAK2L,GAAM7L,EAAKE,IAAM8L,GACtBzC,MAAOsC,GAAM7L,EAAKuJ,MAAQwC,GAC1BzC,OAAQuC,GAAM7L,EAAKsJ,OAAS0C,GAC5B5L,KAAMyL,GAAM7L,EAAKI,KAAO2L,GACxBK,EAAGP,GAAM7L,EAAKI,KAAO2L,GACrBM,EAAGR,GAAM7L,EAAKE,IAAM8L,IC9BT,SAASM,GAAc9b,GACpC,IAAI+b,EAAatM,GAAsBzP,GAGnC0b,EAAQ1b,EAAQyb,YAChBE,EAAS3b,EAAQ4D,aAUrB,OARI+C,KAAK4N,IAAIwH,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjB/U,KAAK4N,IAAIwH,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAG5b,EAAQgQ,WACX6L,EAAG7b,EAAQ+P,UACX2L,MAAOA,EACPC,OAAQA,GCrBG,SAAS3Y,GAASmU,EAAQ3G,GACvC,IAAIwL,EAAWxL,EAAMlN,aAAekN,EAAMlN,cAE1C,GAAI6T,EAAOnU,SAASwN,GAClB,OAAO,EAEJ,GAAIwL,GAAYjC,GAAaiC,GAAW,CACzC,IAAIjL,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQoG,EAAO8E,WAAWlL,GAC5B,OAAO,EAITA,EAAOA,EAAKtN,YAAcsN,EAAKmL,WACxBnL,GAIb,OAAO,ECpBM,SAASrO,GAAiB1C,GACvC,OAAOyZ,GAAUzZ,GAAS0C,iBAAiB1C,GCD9B,SAASmc,GAAenc,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMyG,QAAQ8S,GAAYvZ,KAAa,ECDjD,SAASoc,GAAmBpc,GAEzC,QAASe,GAAUf,GAAWA,EAAQ2Z,cACtC3Z,EAAQS,WAAasD,OAAOtD,UAAU2C,gBCDzB,SAASiZ,GAAcrc,GACpC,MAA6B,SAAzBuZ,GAAYvZ,GACPA,EAMPA,EAAQsc,cACRtc,EAAQyD,aACRsW,GAAa/Z,GAAWA,EAAQkc,KAAO,OAEvCE,GAAmBpc,GCRvB,SAASuc,GAAoBvc,GAC3B,OAAK6Z,GAAc7Z,IACoB,UAAvC0C,GAAiB1C,GAAS8P,SAInB9P,EAAQwc,aAHN,KAwCI,SAASC,GAAgBzc,GAItC,IAHA,IAAI+D,EAAS0V,GAAUzZ,GACnBwc,EAAeD,GAAoBvc,GAEhCwc,GAAgBL,GAAeK,IAA6D,WAA5C9Z,GAAiB8Z,GAAc1M,UACpF0M,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9BjD,GAAYiD,IAA0D,SAA9BjD,GAAYiD,IAAwE,WAA5C9Z,GAAiB8Z,GAAc1M,UAC3H/L,EAGFyY,GA5CT,SAA4Bxc,GAC1B,IAAI0c,GAAsE,IAA1DvJ,UAAUwJ,UAAUxa,cAAcsE,QAAQ,WAG1D,IAFuD,IAA5C0M,UAAUwJ,UAAUlW,QAAQ,YAE3BoT,GAAc7Z,IAII,UAFX0C,GAAiB1C,GAEnB8P,SACb,OAAO,KAMX,IAFA,IAAI8M,EAAcP,GAAcrc,GAEzB6Z,GAAc+C,IAAgB,CAAC,OAAQ,QAAQnW,QAAQ8S,GAAYqD,IAAgB,GAAG,CAC3F,IAAIC,EAAMna,GAAiBka,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAevW,QAAQoW,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAI1N,QAAyB,SAAf0N,EAAI1N,OACjO,OAAOyN,EAEPA,EAAcA,EAAYnZ,WAI9B,OAAO,KAiBgByZ,CAAmBld,IAAY+D,EC9DzC,SAASoZ,GAAyB/D,GAC/C,MAAO,CAAC,MAAO,UAAU3S,QAAQ2S,IAAc,EAAI,IAAM,ICDpD,IAAIxS,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXwU,GAAQ1U,KAAK0U,MCDT,SAAS+B,GAAOvW,EAAK/E,EAAO8E,GACzC,OAAOyW,GAAQxW,EAAKyW,GAAQxb,EAAO8E,ICDtB,SAAS2W,GAAmBC,GACzC,OAAO/b,OAAO8Y,OAAO,GCDd,CACL7K,IAAK,EACLqJ,MAAO,EACPD,OAAQ,EACRlJ,KAAM,GDHuC4N,GEFlC,SAASC,GAAgB3b,EAAOJ,GAC7C,OAAOA,EAAKwX,QAAO,SAAUwE,EAASlS,GAEpC,OADAkS,EAAQlS,GAAO1J,EACR4b,IACN,ICwFL,IAAAC,GAAe,CACbnZ,KAAM,QACNyV,SAAS,EACTC,MAAO,OACPvV,GA9EF,SAAewV,GACb,IAAIyD,EAEAxD,EAAQD,EAAKC,MACb5V,EAAO2V,EAAK3V,KACZoW,EAAUT,EAAKS,QACfiD,EAAezD,EAAMC,SAASU,MAC9B+C,EAAgB1D,EAAM2D,cAAcD,cACpCE,EAAgB5C,GAAiBhB,EAAMhB,WACvC6E,EAAOd,GAAyBa,GAEhC/V,EADa,CAAC2H,GAAMmJ,IAAOtS,QAAQuX,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBU,EAAS9D,GAItD,OAAOmD,GAAsC,iBAH7CW,EAA6B,mBAAZA,EAAyBA,EAAQzc,OAAO8Y,OAAO,GAAIH,EAAM+D,MAAO,CAC/E/E,UAAWgB,EAAMhB,aACb8E,GACkDA,EAAUT,GAAgBS,EAASlF,KAoBvEoF,CAAgBxD,EAAQsD,QAAS9D,GACjDiE,EAAYvC,GAAc+B,GAC1BS,EAAmB,MAATL,EAAevO,GAAME,GAC/B2O,EAAmB,MAATN,EAAenF,GAASC,GAClCyF,EAAUpE,EAAM+D,MAAMnD,UAAU/S,GAAOmS,EAAM+D,MAAMnD,UAAUiD,GAAQH,EAAcG,GAAQ7D,EAAM+D,MAAMxD,OAAO1S,GAC9GwW,EAAYX,EAAcG,GAAQ7D,EAAM+D,MAAMnD,UAAUiD,GACxDS,EAAoBjC,GAAgBoB,GACpCc,EAAaD,EAA6B,MAATT,EAAeS,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9C5X,EAAM2W,EAAcc,GACpB1X,EAAM+X,EAAaN,EAAUpW,GAAOuV,EAAce,GAClDQ,EAASJ,EAAa,EAAIN,EAAUpW,GAAO,EAAI6W,EAC/CvP,EAAS6N,GAAOvW,EAAKkY,EAAQnY,GAE7BoY,EAAWf,EACf7D,EAAM2D,cAAcvZ,KAASoZ,EAAwB,IAA0BoB,GAAYzP,EAAQqO,EAAsBqB,aAAe1P,EAASwP,EAAQnB,KA6CzJpD,OA1CF,SAAgBC,GACd,IAAIL,EAAQK,EAAML,MAEd8E,EADUzE,EAAMG,QACW5a,QAC3B6d,OAAoC,IAArBqB,EAA8B,sBAAwBA,EAErD,MAAhBrB,IAKwB,iBAAjBA,IACTA,EAAezD,EAAMC,SAASM,OAAOja,cAAcmd,MAahD7a,GAASoX,EAAMC,SAASM,OAAQkD,KAQrCzD,EAAMC,SAASU,MAAQ8C,IAUvB1C,SAAU,CAAC,iBACXgE,iBAAkB,CAAC,oBCnGN,SAASC,GAAahG,GACnC,OAAOA,EAAU9Y,MAAM,KAAK,GCQ9B,IAAI+e,GAAa,CACf3P,IAAK,OACLqJ,MAAO,OACPD,OAAQ,OACRlJ,KAAM,QAgBD,SAAS0P,GAAY7E,GAC1B,IAAI8E,EAEA5E,EAASF,EAAME,OACf6E,EAAa/E,EAAM+E,WACnBpG,EAAYqB,EAAMrB,UAClBqG,EAAYhF,EAAMgF,UAClBC,EAAUjF,EAAMiF,QAChB5P,EAAW2K,EAAM3K,SACjB6P,EAAkBlF,EAAMkF,gBACxBC,EAAWnF,EAAMmF,SACjBC,EAAepF,EAAMoF,aAErBC,GAAyB,IAAjBD,EAxBd,SAA2B1F,GACzB,IAAIyB,EAAIzB,EAAKyB,EACTC,EAAI1B,EAAK0B,EAETkE,EADMhc,OACIic,kBAAoB,EAClC,MAAO,CACLpE,EAAGP,GAAMA,GAAMO,EAAImE,GAAOA,IAAQ,EAClClE,EAAGR,GAAMA,GAAMQ,EAAIkE,GAAOA,IAAQ,GAiBAE,CAAkBP,GAAmC,mBAAjBG,EAA8BA,EAAaH,GAAWA,EAC1HQ,EAAUJ,EAAMlE,EAChBA,OAAgB,IAAZsE,EAAqB,EAAIA,EAC7BC,EAAUL,EAAMjE,EAChBA,OAAgB,IAAZsE,EAAqB,EAAIA,EAE7BC,EAAOV,EAAQzE,eAAe,KAC9BoF,EAAOX,EAAQzE,eAAe,KAC9BqF,EAAQ1Q,GACR2Q,EAAQ7Q,GACR8Q,EAAMzc,OAEV,GAAI6b,EAAU,CACZ,IAAIpD,EAAeC,GAAgB9B,GAC/B8F,EAAa,eACbC,EAAY,cAEZlE,IAAiB/C,GAAUkB,IAGmB,WAA5CjY,GAFJ8Z,EAAeJ,GAAmBzB,IAEC7K,UAAsC,aAAbA,IAC1D2Q,EAAa,eACbC,EAAY,eAKhBlE,EAAeA,EAEXpD,IAAc1J,KAAQ0J,IAAcxJ,IAAQwJ,IAAcL,IAAU0G,IAAcxK,MACpFsL,EAAQzH,GAER+C,GAAKW,EAAaiE,GAAcjB,EAAW7D,OAC3CE,GAAK8D,EAAkB,GAAK,GAG1BvG,IAAcxJ,KAASwJ,IAAc1J,IAAO0J,IAAcN,IAAW2G,IAAcxK,MACrFqL,EAAQvH,GAER6C,GAAKY,EAAakE,GAAalB,EAAW9D,MAC1CE,GAAK+D,EAAkB,GAAK,GAIhC,IAKMgB,EALFC,EAAenf,OAAO8Y,OAAO,CAC/BzK,SAAUA,GACT8P,GAAYP,IAEf,OAAIM,EAGKle,OAAO8Y,OAAO,GAAIqG,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe7D,WAAa0D,EAAIR,kBAAoB,IAAM,EAAI,aAAepE,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAU8E,IAG5Rlf,OAAO8Y,OAAO,GAAIqG,IAAerB,EAAkB,IAAoBgB,GAASF,EAAOxE,EAAI,KAAO,GAAI0D,EAAgBe,GAASF,EAAOxE,EAAI,KAAO,GAAI2D,EAAgBzC,UAAY,GAAIyC,IAuD9L,IAAAsB,GAAe,CACbrc,KAAM,gBACNyV,SAAS,EACTC,MAAO,cACPvV,GAxDF,SAAuBmc,GACrB,IAAI1G,EAAQ0G,EAAM1G,MACdQ,EAAUkG,EAAMlG,QAChBmG,EAAwBnG,EAAQ+E,gBAChCA,OAA4C,IAA1BoB,GAA0CA,EAC5DC,EAAoBpG,EAAQgF,SAC5BA,OAAiC,IAAtBoB,GAAsCA,EACjDC,EAAwBrG,EAAQiF,aAChCA,OAAyC,IAA1BoB,GAA0CA,EAYzDL,EAAe,CACjBxH,UAAWgC,GAAiBhB,EAAMhB,WAClCqG,UAAWL,GAAahF,EAAMhB,WAC9BuB,OAAQP,EAAMC,SAASM,OACvB6E,WAAYpF,EAAM+D,MAAMxD,OACxBgF,gBAAiBA,GAGsB,MAArCvF,EAAM2D,cAAcD,gBACtB1D,EAAME,OAAOK,OAASlZ,OAAO8Y,OAAO,GAAIH,EAAME,OAAOK,OAAQ2E,GAAY7d,OAAO8Y,OAAO,GAAIqG,EAAc,CACvGlB,QAAStF,EAAM2D,cAAcD,cAC7BhO,SAAUsK,EAAMQ,QAAQC,SACxB+E,SAAUA,EACVC,aAAcA,OAIe,MAA7BzF,EAAM2D,cAAchD,QACtBX,EAAME,OAAOS,MAAQtZ,OAAO8Y,OAAO,GAAIH,EAAME,OAAOS,MAAOuE,GAAY7d,OAAO8Y,OAAO,GAAIqG,EAAc,CACrGlB,QAAStF,EAAM2D,cAAchD,MAC7BjL,SAAU,WACV8P,UAAU,EACVC,aAAcA,OAIlBzF,EAAMnL,WAAW0L,OAASlZ,OAAO8Y,OAAO,GAAIH,EAAMnL,WAAW0L,OAAQ,CACnEuG,wBAAyB9G,EAAMhB,aAUjClL,KAAM,IC1JJiT,GAAU,CACZA,SAAS,GAsCXC,GAAe,CACb5c,KAAM,iBACNyV,SAAS,EACTC,MAAO,QACPvV,GAAI,aACJ6V,OAxCF,SAAgBL,GACd,IAAIC,EAAQD,EAAKC,MACbpO,EAAWmO,EAAKnO,SAChB4O,EAAUT,EAAKS,QACfyG,EAAkBzG,EAAQ0G,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB3G,EAAQ4G,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7Cxd,EAAS0V,GAAUW,EAAMC,SAASM,QAClC8G,EAAgB,GAAGtR,OAAOiK,EAAMqH,cAAczG,UAAWZ,EAAMqH,cAAc9G,QAYjF,OAVI2G,GACFG,EAAc9f,SAAQ,SAAU+f,GAC9BA,EAAa1c,iBAAiB,SAAUgH,EAAS2V,OAAQR,OAIzDK,GACFzd,EAAOiB,iBAAiB,SAAUgH,EAAS2V,OAAQR,IAG9C,WACDG,GACFG,EAAc9f,SAAQ,SAAU+f,GAC9BA,EAAazb,oBAAoB,SAAU+F,EAAS2V,OAAQR,OAI5DK,GACFzd,EAAOkC,oBAAoB,SAAU+F,EAAS2V,OAAQR,MAY1DjT,KAAM,IC/CJ0T,GAAO,CACThS,KAAM,QACNmJ,MAAO,OACPD,OAAQ,MACRpJ,IAAK,UAEQ,SAASmS,GAAqBzI,GAC3C,OAAOA,EAAUjQ,QAAQ,0BAA0B,SAAU2Y,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACT/M,MAAO,MACPI,IAAK,SAEQ,SAAS8M,GAA8B3I,GACpD,OAAOA,EAAUjQ,QAAQ,cAAc,SAAU2Y,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgBtI,GACtC,IAAI8G,EAAM/G,GAAUC,GAGpB,MAAO,CACLuI,WAHezB,EAAI3Q,YAInBqS,UAHc1B,EAAI7Q,aCDP,SAASwS,GAAoBniB,GAQ1C,OAAOyP,GAAsB2M,GAAmBpc,IAAU4P,KAAOoS,GAAgBhiB,GAASiiB,WCV7E,SAASG,GAAepiB,GAErC,IAAIqiB,EAAoB3f,GAAiB1C,GACrCsiB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6BngB,KAAKigB,EAAWE,EAAYD,GCGnD,SAASE,GAAkBziB,EAASoG,GACjD,IAAIsc,OAES,IAATtc,IACFA,EAAO,IAGT,IAAIsb,ECdS,SAASiB,EAAgBjJ,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAajT,QAAQ8S,GAAYG,KAAU,EAEvDA,EAAKC,cAAc3V,KAGxB6V,GAAcH,IAAS0I,GAAe1I,GACjCA,EAGFiJ,EAAgBtG,GAAc3C,IDIlBiJ,CAAgB3iB,GAC/B4iB,EAASlB,KAAqE,OAAlDgB,EAAwB1iB,EAAQ2Z,oBAAyB,EAAS+I,EAAsB1e,MACpHwc,EAAM/G,GAAUiI,GAChB1b,EAAS4c,EAAS,CAACpC,GAAKrQ,OAAOqQ,EAAIqC,gBAAkB,GAAIT,GAAeV,GAAgBA,EAAe,IAAMA,EAC7GoB,EAAc1c,EAAK+J,OAAOnK,GAC9B,OAAO4c,EAASE,EAChBA,EAAY3S,OAAOsS,GAAkBpG,GAAcrW,KExBtC,SAAS+c,GAAiBvT,GACvC,OAAO/N,OAAO8Y,OAAO,GAAI/K,EAAM,CAC7BI,KAAMJ,EAAKoM,EACXlM,IAAKF,EAAKqM,EACV9C,MAAOvJ,EAAKoM,EAAIpM,EAAKkM,MACrB5C,OAAQtJ,EAAKqM,EAAIrM,EAAKmM,SCuB1B,SAASqH,GAA2BhjB,EAASijB,GAC3C,MhCpBoB,agCoBbA,EAA8BF,GC1BxB,SAAyB/iB,GACtC,IAAIwgB,EAAM/G,GAAUzZ,GAChBkjB,EAAO9G,GAAmBpc,GAC1B6iB,EAAiBrC,EAAIqC,eACrBnH,EAAQwH,EAAKrE,YACblD,EAASuH,EAAKtE,aACdhD,EAAI,EACJC,EAAI,EAuBR,OAjBIgH,IACFnH,EAAQmH,EAAenH,MACvBC,EAASkH,EAAelH,OASnB,iCAAiCtZ,KAAK8Q,UAAUwJ,aACnDf,EAAIiH,EAAe7S,WACnB6L,EAAIgH,EAAe9S,YAIhB,CACL2L,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAIuG,GAAoBniB,GAC3B6b,EAAGA,GDRiDsH,CAAgBnjB,IAAY6Z,GAAcoJ,GAdlG,SAAoCjjB,GAClC,IAAIwP,EAAOC,GAAsBzP,GASjC,OARAwP,EAAKE,IAAMF,EAAKE,IAAM1P,EAAQojB,UAC9B5T,EAAKI,KAAOJ,EAAKI,KAAO5P,EAAQqjB,WAChC7T,EAAKsJ,OAAStJ,EAAKE,IAAM1P,EAAQ4e,aACjCpP,EAAKuJ,MAAQvJ,EAAKI,KAAO5P,EAAQ6e,YACjCrP,EAAKkM,MAAQ1b,EAAQ6e,YACrBrP,EAAKmM,OAAS3b,EAAQ4e,aACtBpP,EAAKoM,EAAIpM,EAAKI,KACdJ,EAAKqM,EAAIrM,EAAKE,IACPF,EAI2G8T,CAA2BL,GAAkBF,GEtBlJ,SAAyB/iB,GACtC,IAAI0iB,EAEAQ,EAAO9G,GAAmBpc,GAC1BujB,EAAYvB,GAAgBhiB,GAC5BgE,EAA0D,OAAlD0e,EAAwB1iB,EAAQ2Z,oBAAyB,EAAS+I,EAAsB1e,KAChG0X,EAAQ9U,GAAIsc,EAAKM,YAAaN,EAAKrE,YAAa7a,EAAOA,EAAKwf,YAAc,EAAGxf,EAAOA,EAAK6a,YAAc,GACvGlD,EAAS/U,GAAIsc,EAAKO,aAAcP,EAAKtE,aAAc5a,EAAOA,EAAKyf,aAAe,EAAGzf,EAAOA,EAAK4a,aAAe,GAC5GhD,GAAK2H,EAAUtB,WAAaE,GAAoBniB,GAChD6b,GAAK0H,EAAUrB,UAMnB,MAJiD,QAA7Cxf,GAAiBsB,GAAQkf,GAAM1O,YACjCoH,GAAKhV,GAAIsc,EAAKrE,YAAa7a,EAAOA,EAAK6a,YAAc,GAAKnD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFG2K6H,CAAgBtH,GAAmBpc,KGzBtM,SAAS2jB,GAAexJ,GACrC,IAOIuF,EAPA1E,EAAYb,EAAKa,UACjBhb,EAAUma,EAAKna,QACfoZ,EAAYe,EAAKf,UACjB4E,EAAgB5E,EAAYgC,GAAiBhC,GAAa,KAC1DqG,EAAYrG,EAAYgG,GAAahG,GAAa,KAClDwK,EAAU5I,EAAUY,EAAIZ,EAAUU,MAAQ,EAAI1b,EAAQ0b,MAAQ,EAC9DmI,EAAU7I,EAAUa,EAAIb,EAAUW,OAAS,EAAI3b,EAAQ2b,OAAS,EAGpE,OAAQqC,GACN,KAAKtO,GACHgQ,EAAU,CACR9D,EAAGgI,EACH/H,EAAGb,EAAUa,EAAI7b,EAAQ2b,QAE3B,MAEF,KAAK7C,GACH4G,EAAU,CACR9D,EAAGgI,EACH/H,EAAGb,EAAUa,EAAIb,EAAUW,QAE7B,MAEF,KAAK5C,GACH2G,EAAU,CACR9D,EAAGZ,EAAUY,EAAIZ,EAAUU,MAC3BG,EAAGgI,GAEL,MAEF,KAAKjU,GACH8P,EAAU,CACR9D,EAAGZ,EAAUY,EAAI5b,EAAQ0b,MACzBG,EAAGgI,GAEL,MAEF,QACEnE,EAAU,CACR9D,EAAGZ,EAAUY,EACbC,EAAGb,EAAUa,GAInB,IAAIiI,EAAW9F,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZ8F,EAAkB,CACpB,IAAI7b,EAAmB,MAAb6b,EAAmB,SAAW,QAExC,OAAQrE,GACN,InClDa,QmCmDXC,EAAQoE,GAAYpE,EAAQoE,IAAa9I,EAAU/S,GAAO,EAAIjI,EAAQiI,GAAO,GAC7E,MAEF,KAAKgN,GACHyK,EAAQoE,GAAYpE,EAAQoE,IAAa9I,EAAU/S,GAAO,EAAIjI,EAAQiI,GAAO,IAOnF,OAAOyX,EC1DM,SAASqE,GAAe3J,EAAOQ,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXqJ,EAAqBD,EAAS5K,UAC9BA,OAAmC,IAAvB6K,EAAgC7J,EAAMhB,UAAY6K,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EpCXY,kBoCWqCA,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EpCZC,WoCY6CA,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EpCbH,SoCa+CA,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAAS9F,QAC5BA,OAA+B,IAArBwG,EAA8B,EAAIA,EAC5ClH,EAAgBD,GAAsC,iBAAZW,EAAuBA,EAAUT,GAAgBS,EAASlF,KACpG2L,EpCnBc,WoCmBDJ,EpClBI,YADH,SoCoBd/E,EAAapF,EAAM+D,MAAMxD,OACzB3a,EAAUoa,EAAMC,SAASoK,EAAcE,EAAaJ,GACpDK,EJoBS,SAAyB5kB,EAASmkB,EAAUE,GACzD,IAAIQ,EAAmC,oBAAbV,EAlB5B,SAA4BnkB,GAC1B,IAAI8kB,EAAkBrC,GAAkBpG,GAAcrc,IAElD+kB,EADoB,CAAC,WAAY,SAASte,QAAQ/D,GAAiB1C,GAAS8P,WAAa,GACnD+J,GAAc7Z,GAAWyc,GAAgBzc,GAAWA,EAE9F,OAAKe,GAAUgkB,GAKRD,EAAgB3V,QAAO,SAAU8T,GACtC,OAAOliB,GAAUkiB,IAAmBjgB,GAASigB,EAAgB8B,IAAmD,SAAhCxL,GAAY0J,MALrF,GAYkD+B,CAAmBhlB,GAAW,GAAGmQ,OAAOgU,GAC/FW,EAAkB,GAAG3U,OAAO0U,EAAqB,CAACR,IAClDY,EAAsBH,EAAgB,GACtCI,EAAeJ,EAAgB5L,QAAO,SAAUiM,EAASlC,GAC3D,IAAIzT,EAAOwT,GAA2BhjB,EAASijB,GAK/C,OAJAkC,EAAQzV,IAAM9I,GAAI4I,EAAKE,IAAKyV,EAAQzV,KACpCyV,EAAQpM,MAAQlS,GAAI2I,EAAKuJ,MAAOoM,EAAQpM,OACxCoM,EAAQrM,OAASjS,GAAI2I,EAAKsJ,OAAQqM,EAAQrM,QAC1CqM,EAAQvV,KAAOhJ,GAAI4I,EAAKI,KAAMuV,EAAQvV,MAC/BuV,IACNnC,GAA2BhjB,EAASilB,IAKvC,OAJAC,EAAaxJ,MAAQwJ,EAAanM,MAAQmM,EAAatV,KACvDsV,EAAavJ,OAASuJ,EAAapM,OAASoM,EAAaxV,IACzDwV,EAAatJ,EAAIsJ,EAAatV,KAC9BsV,EAAarJ,EAAIqJ,EAAaxV,IACvBwV,EIpCkBE,CAAgBrkB,GAAUf,GAAWA,EAAUA,EAAQqlB,gBAAkBjJ,GAAmBhC,EAAMC,SAASM,QAASwJ,EAAUE,GACnJiB,EAAsB7V,GAAsB2K,EAAMC,SAASW,WAC3D8C,EAAgB6F,GAAe,CACjC3I,UAAWsK,EACXtlB,QAASwf,EACT3E,SAAU,WACVzB,UAAWA,IAETmM,EAAmBxC,GAAiBthB,OAAO8Y,OAAO,GAAIiF,EAAY1B,IAClE0H,EpC/Bc,WoC+BMjB,EAA4BgB,EAAmBD,EAGnEG,EAAkB,CACpB/V,IAAKkV,EAAmBlV,IAAM8V,EAAkB9V,IAAM8N,EAAc9N,IACpEoJ,OAAQ0M,EAAkB1M,OAAS8L,EAAmB9L,OAAS0E,EAAc1E,OAC7ElJ,KAAMgV,EAAmBhV,KAAO4V,EAAkB5V,KAAO4N,EAAc5N,KACvEmJ,MAAOyM,EAAkBzM,MAAQ6L,EAAmB7L,MAAQyE,EAAczE,OAExE2M,EAAatL,EAAM2D,cAAcxO,OAErC,GpC1CkB,WoC0CdgV,GAA6BmB,EAAY,CAC3C,IAAInW,EAASmW,EAAWtM,GACxB3X,OAAOC,KAAK+jB,GAAiB9jB,SAAQ,SAAU6J,GAC7C,IAAIma,EAAW,CAAC5M,GAAOD,IAAQrS,QAAQ+E,IAAQ,EAAI,GAAK,EACpDyS,EAAO,CAACvO,GAAKoJ,IAAQrS,QAAQ+E,IAAQ,EAAI,IAAM,IACnDia,EAAgBja,IAAQ+D,EAAO0O,GAAQ0H,KAI3C,OAAOF,ECzDM,SAASG,GAAqBxL,EAAOQ,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIoJ,EAAWpJ,EACXxB,EAAY4K,EAAS5K,UACrB+K,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBnG,EAAU8F,EAAS9F,QACnB2H,EAAiB7B,EAAS6B,eAC1BC,EAAwB9B,EAAS+B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3ErG,EAAYL,GAAahG,GACzBC,EAAaoG,EAAYoG,EAAiB5M,GAAsBA,GAAoB9J,QAAO,SAAUiK,GACvG,OAAOgG,GAAahG,KAAeqG,KAChCzG,GACDiN,EAAoB5M,EAAWlK,QAAO,SAAUiK,GAClD,OAAO2M,EAAsBtf,QAAQ2S,IAAc,KAGpB,IAA7B6M,EAAkB7kB,SACpB6kB,EAAoB5M,GAQtB,IAAI6M,EAAYD,EAAkB/M,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa2K,GAAe3J,EAAO,CACrChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,IACR9C,GAAiBhC,IACbD,IACN,IACH,OAAO1X,OAAOC,KAAKwkB,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,IAAAC,GAAe,CACb9hB,KAAM,OACNyV,SAAS,EACTC,MAAO,OACPvV,GA5HF,SAAcwV,GACZ,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfpW,EAAO2V,EAAK3V,KAEhB,IAAI4V,EAAM2D,cAAcvZ,GAAM+hB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB5L,EAAQkJ,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB9L,EAAQ+L,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BjM,EAAQkM,mBACtC5I,EAAUtD,EAAQsD,QAClBiG,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBsC,EAAwBnM,EAAQiL,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBnL,EAAQmL,sBAChCiB,EAAqB5M,EAAMQ,QAAQxB,UACnC4E,EAAgB5C,GAAiB4L,GAEjCF,EAAqBD,IADH7I,IAAkBgJ,GACqCnB,EAjC/E,SAAuCzM,GACrC,GtCLgB,SsCKZgC,GAAiBhC,GACnB,MAAO,GAGT,IAAI6N,EAAoBpF,GAAqBzI,GAC7C,MAAO,CAAC2I,GAA8B3I,GAAY6N,EAAmBlF,GAA8BkF,IA2BwCC,CAA8BF,GAA3E,CAACnF,GAAqBmF,KAChH3N,EAAa,CAAC2N,GAAoB7W,OAAO2W,GAAoB5N,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIhJ,OtCvCG,SsCuCIiL,GAAiBhC,GAAsBwM,GAAqBxL,EAAO,CACnFhB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACT2H,eAAgBA,EAChBE,sBAAuBA,IACpB3M,KACJ,IACC+N,EAAgB/M,EAAM+D,MAAMnD,UAC5BwE,EAAapF,EAAM+D,MAAMxD,OACzByM,EAAY,IAAIvb,IAChBwb,GAAqB,EACrBC,EAAwBjO,EAAW,GAE9BrR,EAAI,EAAGA,EAAIqR,EAAWjY,OAAQ4G,IAAK,CAC1C,IAAIoR,EAAYC,EAAWrR,GAEvBuf,EAAiBnM,GAAiBhC,GAElCoO,EtCzDW,UsCyDQpI,GAAahG,GAChCqO,EAAa,CAAC/X,GAAKoJ,IAAQrS,QAAQ8gB,IAAmB,EACtDtf,EAAMwf,EAAa,QAAU,SAC7BnF,EAAWyB,GAAe3J,EAAO,CACnChB,UAAWA,EACX+K,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACbvG,QAASA,IAEPwJ,EAAoBD,EAAaD,EAAmBzO,GAAQnJ,GAAO4X,EAAmB1O,GAASpJ,GAE/FyX,EAAclf,GAAOuX,EAAWvX,KAClCyf,EAAoB7F,GAAqB6F,IAG3C,IAAIC,EAAmB9F,GAAqB6F,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAO3iB,KAAKqd,EAASiF,IAAmB,GAGtCX,GACFgB,EAAO3iB,KAAKqd,EAASoF,IAAsB,EAAGpF,EAASqF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFR,EAAwBlO,EACxBiO,GAAqB,EACrB,MAGFD,EAAUrb,IAAIqN,EAAWwO,GAG3B,GAAIP,EAqBF,IAnBA,IAEIU,EAAQ,SAAeC,GACzB,IAAIC,EAAmB5O,EAAWnJ,MAAK,SAAUkJ,GAC/C,IAAIwO,EAASR,EAAU1b,IAAI0N,GAE3B,GAAIwO,EACF,OAAOA,EAAOrd,MAAM,EAAGyd,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAX,EAAwBW,EACjB,SAIFD,EAnBYnC,EAAiB,EAAI,EAmBZmC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC5N,EAAMhB,YAAckO,IACtBlN,EAAM2D,cAAcvZ,GAAM+hB,OAAQ,EAClCnM,EAAMhB,UAAYkO,EAClBlN,EAAM8N,OAAQ,KAUhB/I,iBAAkB,CAAC,UACnBjR,KAAM,CACJqY,OAAO,IC7IX,SAAS4B,GAAe7F,EAAU9S,EAAM4Y,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjBxM,EAAG,EACHC,EAAG,IAIA,CACLnM,IAAK4S,EAAS5S,IAAMF,EAAKmM,OAASyM,EAAiBvM,EACnD9C,MAAOuJ,EAASvJ,MAAQvJ,EAAKkM,MAAQ0M,EAAiBxM,EACtD9C,OAAQwJ,EAASxJ,OAAStJ,EAAKmM,OAASyM,EAAiBvM,EACzDjM,KAAM0S,EAAS1S,KAAOJ,EAAKkM,MAAQ0M,EAAiBxM,GAIxD,SAASyM,GAAsB/F,GAC7B,MAAO,CAAC5S,GAAKqJ,GAAOD,GAAQlJ,IAAM0Y,MAAK,SAAUC,GAC/C,OAAOjG,EAASiG,IAAS,KAiC7B,IAAAC,GAAe,CACbhkB,KAAM,OACNyV,SAAS,EACTC,MAAO,OACPiF,iBAAkB,CAAC,mBACnBxa,GAlCF,SAAcwV,GACZ,IAAIC,EAAQD,EAAKC,MACb5V,EAAO2V,EAAK3V,KACZ2iB,EAAgB/M,EAAM+D,MAAMnD,UAC5BwE,EAAapF,EAAM+D,MAAMxD,OACzByN,EAAmBhO,EAAM2D,cAAc0K,gBACvCC,EAAoB3E,GAAe3J,EAAO,CAC5CmK,eAAgB,cAEdoE,EAAoB5E,GAAe3J,EAAO,CAC5CqK,aAAa,IAEXmE,EAA2BT,GAAeO,EAAmBvB,GAC7D0B,EAAsBV,GAAeQ,EAAmBnJ,EAAY4I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7CzO,EAAM2D,cAAcvZ,GAAQ,CAC1BokB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB3O,EAAMnL,WAAW0L,OAASlZ,OAAO8Y,OAAO,GAAIH,EAAMnL,WAAW0L,OAAQ,CACnEqO,+BAAgCF,EAChCG,sBAAuBF,MCH3BG,GAAe,CACb1kB,KAAM,SACNyV,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXxW,GA5BF,SAAgB8V,GACd,IAAIL,EAAQK,EAAML,MACdQ,EAAUH,EAAMG,QAChBpW,EAAOiW,EAAMjW,KACb2kB,EAAkBvO,EAAQrL,OAC1BA,OAA6B,IAApB4Z,EAA6B,CAAC,EAAG,GAAKA,EAC/Cjb,EAAOmL,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW+E,EAAO5O,GACxD,IAAIyO,EAAgB5C,GAAiBhC,GACjCgQ,EAAiB,CAACxZ,GAAMF,IAAKjJ,QAAQuX,IAAkB,GAAK,EAAI,EAEhE7D,EAAyB,mBAAX5K,EAAwBA,EAAO9N,OAAO8Y,OAAO,GAAI4D,EAAO,CACxE/E,UAAWA,KACP7J,EACF8Z,EAAWlP,EAAK,GAChBmP,EAAWnP,EAAK,GAIpB,OAFAkP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACxZ,GAAMmJ,IAAOtS,QAAQuX,IAAkB,EAAI,CACjDpC,EAAG0N,EACHzN,EAAGwN,GACD,CACFzN,EAAGyN,EACHxN,EAAGyN,GAWcC,CAAwBnQ,EAAWgB,EAAM+D,MAAO5O,GAC1D4J,IACN,IACCqQ,EAAwBtb,EAAKkM,EAAMhB,WACnCwC,EAAI4N,EAAsB5N,EAC1BC,EAAI2N,EAAsB3N,EAEW,MAArCzB,EAAM2D,cAAcD,gBACtB1D,EAAM2D,cAAcD,cAAclC,GAAKA,EACvCxB,EAAM2D,cAAcD,cAAcjC,GAAKA,GAGzCzB,EAAM2D,cAAcvZ,GAAQ0J,ICxB9Bub,GAAe,CACbjlB,KAAM,gBACNyV,SAAS,EACTC,MAAO,OACPvV,GApBF,SAAuBwV,GACrB,IAAIC,EAAQD,EAAKC,MACb5V,EAAO2V,EAAK3V,KAKhB4V,EAAM2D,cAAcvZ,GAAQmf,GAAe,CACzC3I,UAAWZ,EAAM+D,MAAMnD,UACvBhb,QAASoa,EAAM+D,MAAMxD,OACrBE,SAAU,WACVzB,UAAWgB,EAAMhB,aAUnBlL,KAAM,IC6FRwb,GAAe,CACbllB,KAAM,kBACNyV,SAAS,EACTC,MAAO,OACPvV,GA5GF,SAAyBwV,GACvB,IAAIC,EAAQD,EAAKC,MACbQ,EAAUT,EAAKS,QACfpW,EAAO2V,EAAK3V,KACZgiB,EAAoB5L,EAAQkJ,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB9L,EAAQ+L,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDvC,EAAWvJ,EAAQuJ,SACnBE,EAAezJ,EAAQyJ,aACvBI,EAAc7J,EAAQ6J,YACtBvG,EAAUtD,EAAQsD,QAClByL,EAAkB/O,EAAQgP,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBjP,EAAQkP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDvH,EAAWyB,GAAe3J,EAAO,CACnC+J,SAAUA,EACVE,aAAcA,EACdnG,QAASA,EACTuG,YAAaA,IAEXzG,EAAgB5C,GAAiBhB,EAAMhB,WACvCqG,EAAYL,GAAahF,EAAMhB,WAC/B2Q,GAAmBtK,EACnBqE,EAAW3G,GAAyBa,GACpC2I,ECrCY,MDqCS7C,ECrCH,IAAM,IDsCxBhG,EAAgB1D,EAAM2D,cAAcD,cACpCqJ,EAAgB/M,EAAM+D,MAAMnD,UAC5BwE,EAAapF,EAAM+D,MAAMxD,OACzBqP,EAA4C,mBAAjBF,EAA8BA,EAAaroB,OAAO8Y,OAAO,GAAIH,EAAM+D,MAAO,CACvG/E,UAAWgB,EAAMhB,aACb0Q,EACF5b,EAAO,CACT0N,EAAG,EACHC,EAAG,GAGL,GAAKiC,EAAL,CAIA,GAAI2I,GAAiBG,EAAc,CACjC,IAAIqD,EAAwB,MAAbnG,EAAmBpU,GAAME,GACpCsa,EAAuB,MAAbpG,EAAmBhL,GAASC,GACtC9Q,EAAmB,MAAb6b,EAAmB,SAAW,QACpCvU,EAASuO,EAAcgG,GACvBjd,EAAMiX,EAAcgG,GAAYxB,EAAS2H,GACzCrjB,EAAMkX,EAAcgG,GAAYxB,EAAS4H,GACzCC,EAAWP,GAAUpK,EAAWvX,GAAO,EAAI,EAC3CmiB,E1CxDW,U0CwDF3K,EAAsB0H,EAAclf,GAAOuX,EAAWvX,GAC/DoiB,E1CzDW,U0CyDF5K,GAAuBD,EAAWvX,IAAQkf,EAAclf,GAGjE4V,EAAezD,EAAMC,SAASU,MAC9BsD,EAAYuL,GAAU/L,EAAe/B,GAAc+B,GAAgB,CACrEnC,MAAO,EACPC,OAAQ,GAEN2O,EAAqBlQ,EAAM2D,cAAc,oBAAsB3D,EAAM2D,cAAc,oBAAoBG,QxBtEtG,CACLxO,IAAK,EACLqJ,MAAO,EACPD,OAAQ,EACRlJ,KAAM,GwBmEF2a,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWrN,GAAO,EAAG+J,EAAclf,GAAMoW,EAAUpW,IACnDyiB,EAAYX,EAAkB5C,EAAclf,GAAO,EAAIkiB,EAAWM,EAAWF,EAAkBP,EAAoBI,EAASK,EAAWF,EAAkBP,EACzJW,EAAYZ,GAAmB5C,EAAclf,GAAO,EAAIkiB,EAAWM,EAAWD,EAAkBR,EAAoBK,EAASI,EAAWD,EAAkBR,EAC1JtL,EAAoBtE,EAAMC,SAASU,OAAS0B,GAAgBrC,EAAMC,SAASU,OAC3E6P,EAAelM,EAAiC,MAAboF,EAAmBpF,EAAkB0E,WAAa,EAAI1E,EAAkB2E,YAAc,EAAI,EAC7HwH,EAAsBzQ,EAAM2D,cAAcxO,OAAS6K,EAAM2D,cAAcxO,OAAO6K,EAAMhB,WAAW0K,GAAY,EAC3GgH,EAAYhN,EAAcgG,GAAY4G,EAAYG,EAAsBD,EACxEG,EAAYjN,EAAcgG,GAAY6G,EAAYE,EAEtD,GAAIpE,EAAe,CACjB,IAAIuE,EAAkB5N,GAAOwM,EAAStM,GAAQzW,EAAKikB,GAAajkB,EAAK0I,EAAQqa,EAASvM,GAAQzW,EAAKmkB,GAAankB,GAChHkX,EAAcgG,GAAYkH,EAC1B9c,EAAK4V,GAAYkH,EAAkBzb,EAGrC,GAAIqX,EAAc,CAChB,IAAIqE,EAAyB,MAAbnH,EAAmBpU,GAAME,GAErCsb,EAAwB,MAAbpH,EAAmBhL,GAASC,GAEvCoS,EAAUrN,EAAc6I,GAExByE,EAAOD,EAAU7I,EAAS2I,GAE1BI,GAAOF,EAAU7I,EAAS4I,GAE1BI,GAAmBlO,GAAOwM,EAAStM,GAAQ8N,EAAMN,GAAaM,EAAMD,EAASvB,EAASvM,GAAQgO,GAAMN,GAAaM,IAErHvN,EAAc6I,GAAW2E,GACzBpd,EAAKyY,GAAW2E,GAAmBH,GAIvC/Q,EAAM2D,cAAcvZ,GAAQ0J,IAS5BiR,iBAAkB,CAAC,WExGN,SAASoM,GAAiBC,EAAyBhP,EAAciP,QAC9D,IAAZA,IACFA,GAAU,GAGZ,IClBoC/R,ECJO1Z,EFsBvC0rB,EAA0B7R,GAAc2C,GACxCmP,EAAuB9R,GAAc2C,IAf3C,SAAyBxc,GACvB,IAAIwP,EAAOxP,EAAQyP,wBACf8L,EAAS/L,EAAKkM,MAAQ1b,EAAQyb,aAAe,EAC7CD,EAAShM,EAAKmM,OAAS3b,EAAQ4D,cAAgB,EACnD,OAAkB,IAAX2X,GAA2B,IAAXC,EAWmCoQ,CAAgBpP,GACtEpZ,EAAkBgZ,GAAmBI,GACrChN,EAAOC,GAAsB+b,EAAyBG,GACtDrK,EAAS,CACXW,WAAY,EACZC,UAAW,GAETxC,EAAU,CACZ9D,EAAG,EACHC,EAAG,GAkBL,OAfI6P,IAA4BA,IAA4BD,MACxB,SAA9BlS,GAAYiD,IAChB4F,GAAehf,MACbke,GClCgC5H,EDkCT8C,KCjCd/C,GAAUC,IAAUG,GAAcH,GCJxC,CACLuI,YAFyCjiB,EDQb0Z,GCNRuI,WACpBC,UAAWliB,EAAQkiB,WDGZF,GAAgBtI,IDmCnBG,GAAc2C,KAChBkD,EAAUjQ,GAAsB+M,GAAc,IACtCZ,GAAKY,EAAa6G,WAC1B3D,EAAQ7D,GAAKW,EAAa4G,WACjBhgB,IACTsc,EAAQ9D,EAAIuG,GAAoB/e,KAI7B,CACLwY,EAAGpM,EAAKI,KAAO0R,EAAOW,WAAavC,EAAQ9D,EAC3CC,EAAGrM,EAAKE,IAAM4R,EAAOY,UAAYxC,EAAQ7D,EACzCH,MAAOlM,EAAKkM,MACZC,OAAQnM,EAAKmM,QGtCjB,IAAIkQ,GAAkB,CACpBzS,UAAW,SACX0S,UAAW,GACXjR,SAAU,YAGZ,SAASkR,KACP,IAAK,IAAIC,EAAOC,UAAU7qB,OAAQsJ,EAAO,IAAI2B,MAAM2f,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IAC/ExhB,EAAKwhB,GAAQD,UAAUC,GAGzB,OAAQxhB,EAAK4d,MAAK,SAAUtoB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQyP,0BAIhC,SAAS0c,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCX,GAAkBW,EAC3E,OAAO,SAAsBxR,EAAWL,EAAQC,QAC9B,IAAZA,IACFA,EAAU6R,GAGZ,IC/C6B9nB,EAC3B+nB,ED8CEtS,EAAQ,CACVhB,UAAW,SACXuT,iBAAkB,GAClB/R,QAASnZ,OAAO8Y,OAAO,GAAIsR,GAAiBY,GAC5C1O,cAAe,GACf1D,SAAU,CACRW,UAAWA,EACXL,OAAQA,GAEV1L,WAAY,GACZqL,OAAQ,IAENsS,EAAmB,GACnBC,GAAc,EACd7gB,EAAW,CACboO,MAAOA,EACP0S,WAAY,SAAoBC,GAC9B,IAAInS,EAAsC,mBAArBmS,EAAkCA,EAAiB3S,EAAMQ,SAAWmS,EACzFC,IACA5S,EAAMQ,QAAUnZ,OAAO8Y,OAAO,GAAIkS,EAAgBrS,EAAMQ,QAASA,GACjER,EAAMqH,cAAgB,CACpBzG,UAAWja,GAAUia,GAAayH,GAAkBzH,GAAaA,EAAUqK,eAAiB5C,GAAkBzH,EAAUqK,gBAAkB,GAC1I1K,OAAQ8H,GAAkB9H,IAI5B,IEzE4BmR,EAC9BmB,EFwEMN,EGvCG,SAAwBb,GAErC,IAAIa,EAlCN,SAAeb,GACb,IAAI3a,EAAM,IAAItF,IACVqhB,EAAU,IAAI1lB,IACd2lB,EAAS,GA0Bb,OAzBArB,EAAUnqB,SAAQ,SAAUyrB,GAC1Bjc,EAAIpF,IAAIqhB,EAAS5oB,KAAM4oB,MAkBzBtB,EAAUnqB,SAAQ,SAAUyrB,GACrBF,EAAQxkB,IAAI0kB,EAAS5oB,OAhB5B,SAAS2hB,EAAKiH,GACZF,EAAQ7X,IAAI+X,EAAS5oB,MACN,GAAG2L,OAAOid,EAASjS,UAAY,GAAIiS,EAASjO,kBAAoB,IACtExd,SAAQ,SAAU0rB,GACzB,IAAKH,EAAQxkB,IAAI2kB,GAAM,CACrB,IAAIC,EAAcnc,EAAIzF,IAAI2hB,GAEtBC,GACFnH,EAAKmH,OAIXH,EAAOloB,KAAKmoB,GAMVjH,CAAKiH,MAGFD,EAKgB/Y,CAAM0X,GAE7B,OAAOxS,GAAeJ,QAAO,SAAUC,EAAKe,GAC1C,OAAOf,EAAIhJ,OAAOwc,EAAiBxd,QAAO,SAAUie,GAClD,OAAOA,EAASlT,QAAUA,QAE3B,IH+B0BqT,EEzEKzB,EFyEsB,GAAG3b,OAAOoc,EAAkBnS,EAAMQ,QAAQkR,WExE9FmB,EAASnB,EAAU5S,QAAO,SAAU+T,EAAQO,GAC9C,IAAIC,EAAWR,EAAOO,EAAQhpB,MAK9B,OAJAyoB,EAAOO,EAAQhpB,MAAQipB,EAAWhsB,OAAO8Y,OAAO,GAAIkT,EAAUD,EAAS,CACrE5S,QAASnZ,OAAO8Y,OAAO,GAAIkT,EAAS7S,QAAS4S,EAAQ5S,SACrD1M,KAAMzM,OAAO8Y,OAAO,GAAIkT,EAASvf,KAAMsf,EAAQtf,QAC5Csf,EACEP,IACN,IAEIxrB,OAAOC,KAAKurB,GAAQ9b,KAAI,SAAU3F,GACvC,OAAOyhB,EAAOzhB,QFuGV,OAvCA4O,EAAMuS,iBAAmBA,EAAiBxd,QAAO,SAAUue,GACzD,OAAOA,EAAEzT,WAqJbG,EAAMuS,iBAAiBhrB,SAAQ,SAAUme,GACvC,IAAItb,EAAOsb,EAAMtb,KACbmpB,EAAgB7N,EAAMlF,QACtBA,OAA4B,IAAlB+S,EAA2B,GAAKA,EAC1CnT,EAASsF,EAAMtF,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIoT,EAAYpT,EAAO,CACrBJ,MAAOA,EACP5V,KAAMA,EACNwH,SAAUA,EACV4O,QAASA,IAKXgS,EAAiB3nB,KAAK2oB,GAFT,kBA7HR5hB,EAAS2V,UAOlBkM,YAAa,WACX,IAAIhB,EAAJ,CAIA,IAAIiB,EAAkB1T,EAAMC,SACxBW,EAAY8S,EAAgB9S,UAC5BL,EAASmT,EAAgBnT,OAG7B,GAAKoR,GAAiB/Q,EAAWL,GAAjC,CASAP,EAAM+D,MAAQ,CACZnD,UAAWuQ,GAAiBvQ,EAAWyB,GAAgB9B,GAAoC,UAA3BP,EAAMQ,QAAQC,UAC9EF,OAAQmB,GAAcnB,IAOxBP,EAAM8N,OAAQ,EACd9N,EAAMhB,UAAYgB,EAAMQ,QAAQxB,UAKhCgB,EAAMuS,iBAAiBhrB,SAAQ,SAAUyrB,GACvC,OAAOhT,EAAM2D,cAAcqP,EAAS5oB,MAAQ/C,OAAO8Y,OAAO,GAAI6S,EAASlf,SAIzE,IAAK,IAAI1H,EAAQ,EAAGA,EAAQ4T,EAAMuS,iBAAiBvrB,OAAQoF,IAUzD,IAAoB,IAAhB4T,EAAM8N,MAAV,CAMA,IAAI6F,EAAwB3T,EAAMuS,iBAAiBnmB,GAC/C7B,EAAKopB,EAAsBppB,GAC3BqpB,EAAyBD,EAAsBnT,QAC/CoJ,OAAsC,IAA3BgK,EAAoC,GAAKA,EACpDxpB,EAAOupB,EAAsBvpB,KAEf,mBAAPG,IACTyV,EAAQzV,EAAG,CACTyV,MAAOA,EACPQ,QAASoJ,EACTxf,KAAMA,EACNwH,SAAUA,KACNoO,QAjBNA,EAAM8N,OAAQ,EACd1hB,GAAS,KAsBfmb,QClM2Bhd,EDkMV,WACf,OAAO,IAAIspB,SAAQ,SAAUC,GAC3BliB,EAAS6hB,cACTK,EAAQ9T,OCnMT,WAUL,OATKsS,IACHA,EAAU,IAAIuB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBzB,OAAUve,EACV+f,EAAQvpB,YAKP+nB,ID4LL0B,QAAS,WACPpB,IACAH,GAAc,IAIlB,IAAKd,GAAiB/Q,EAAWL,GAK/B,OAAO3O,EAmCT,SAASghB,IACPJ,EAAiBjrB,SAAQ,SAAUgD,GACjC,OAAOA,OAETioB,EAAmB,GAGrB,OAvCA5gB,EAAS8gB,WAAWlS,GAASuT,MAAK,SAAU/T,IACrCyS,GAAejS,EAAQyT,eAC1BzT,EAAQyT,cAAcjU,MAqCnBpO,GAGJ,IAAIsiB,GAA4BnC,KI1PnCmC,GAA4BnC,GAAgB,CAC9CI,iBAFqB,CAACnL,GAAgBtD,GAAeyQ,GAAeC,MCMlEF,GAA4BnC,GAAgB,CAC9CI,iBAFqB,CAACnL,GAAgBtD,GAAeyQ,GAAeC,GAAajf,GAAQkf,GAAMhG,GAAiB1N,GAAOhD,2KpDNvG,+BAEC,+BAEU,2BACP,kBACF,mBACG,4DAQC,kBACN,iBACK,uBAEC,kBACN,iBACK,wBAEE,oBACN,mBACK,0JqDGxB,MAYM2W,GAAiB,IAAItsB,OAAQ,4BAqB7BusB,GAAgBzqB,IAAU,UAAY,YACtC0qB,GAAmB1qB,IAAU,YAAc,UAC3C2qB,GAAmB3qB,IAAU,aAAe,eAC5C4qB,GAAsB5qB,IAAU,eAAiB,aACjD6qB,GAAkB7qB,IAAU,aAAe,cAC3C8qB,GAAiB9qB,IAAU,cAAgB,aAE3CoN,GAAU,CACd/B,OAAQ,CAAC,EAAG,GACZ4U,SAAU,kBACVnJ,UAAW,SACXiU,QAAS,UACTC,aAAc,KACdC,WAAW,GAGPtd,GAAc,CAClBtC,OAAQ,0BACR4U,SAAU,mBACVnJ,UAAW,0BACXiU,QAAS,SACTC,aAAc,yBACdC,UAAW,oBASb,MAAMC,WAAiB3iB,EACrBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAKqmB,QAAU,KACfrmB,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKsmB,MAAQtmB,KAAKumB,kBAClBvmB,KAAKwmB,UAAYxmB,KAAKymB,gBAKNne,qBAChB,OAAOA,GAGaO,yBACpB,OAAOA,GAGMpN,kBACb,MArFS,WA0FX4J,SACE,OAAOrF,KAAK8O,WAAa9O,KAAK+O,OAAS/O,KAAKgP,OAG9CA,OACE,GAAIpV,EAAWoG,KAAK2D,WAAa3D,KAAK8O,SAAS9O,KAAKsmB,OAClD,OAGF,MAAMxmB,EAAgB,CACpBA,cAAeE,KAAK2D,UAKtB,GAFkBrD,EAAamB,QAAQzB,KAAK2D,SAvF5B,mBAuFkD7D,GAEpDiC,iBACZ,OAGF,MAAMoM,EAASiY,GAASM,qBAAqB1mB,KAAK2D,UAE9C3D,KAAKwmB,UACP5gB,EAAYC,iBAAiB7F,KAAKsmB,MAAO,SAAU,QAEnDtmB,KAAK2mB,cAAcxY,GAOjB,iBAAkB1W,SAAS2C,kBAC5B+T,EAAOvJ,QA5Fc,gBA6FtB,GAAGuC,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ6V,GAAQlO,EAAaQ,GAAG0N,EAAM,YAAa9T,IAGxDsF,KAAK2D,SAASijB,QACd5mB,KAAK2D,SAAS2B,aAAa,iBAAiB,GAE5CtF,KAAKsmB,MAAMvsB,UAAUsS,IA5GD,QA6GpBrM,KAAK2D,SAAS5J,UAAUsS,IA7GJ,QA8GpB/L,EAAamB,QAAQzB,KAAK2D,SAnHT,oBAmHgC7D,GAGnDiP,OACE,GAAInV,EAAWoG,KAAK2D,YAAc3D,KAAK8O,SAAS9O,KAAKsmB,OACnD,OAGF,MAAMxmB,EAAgB,CACpBA,cAAeE,KAAK2D,UAGtB3D,KAAK6mB,cAAc/mB,GAGrB+D,UACM7D,KAAKqmB,SACPrmB,KAAKqmB,QAAQjB,UAGf9b,MAAMzF,UAGR8U,SACE3Y,KAAKwmB,UAAYxmB,KAAKymB,gBAClBzmB,KAAKqmB,SACPrmB,KAAKqmB,QAAQ1N,SAMjBkO,cAAc/mB,GACMQ,EAAamB,QAAQzB,KAAK2D,SAvJ5B,mBAuJkD7D,GACpDiC,mBAMV,iBAAkBtK,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ6V,GAAQlO,EAAaC,IAAIiO,EAAM,YAAa9T,IAGrDsF,KAAKqmB,SACPrmB,KAAKqmB,QAAQjB,UAGfplB,KAAKsmB,MAAMvsB,UAAUwJ,OA/JD,QAgKpBvD,KAAK2D,SAAS5J,UAAUwJ,OAhKJ,QAiKpBvD,KAAK2D,SAAS2B,aAAa,gBAAiB,SAC5CM,EAAYE,oBAAoB9F,KAAKsmB,MAAO,UAC5ChmB,EAAamB,QAAQzB,KAAK2D,SA1KR,qBA0KgC7D,IAGpDkK,WAAWzR,GAST,GARAA,EAAS,IACJyH,KAAK0D,YAAY4E,WACjB1C,EAAYI,kBAAkBhG,KAAK2D,aACnCpL,GAGLF,EAnMS,WAmMaE,EAAQyH,KAAK0D,YAAYmF,aAEf,iBAArBtQ,EAAOyZ,YAA2Bja,EAAUQ,EAAOyZ,YACV,mBAA3CzZ,EAAOyZ,UAAUvL,sBAGxB,MAAM,IAAInN,UAzMH,WAyMqBC,cAAP,kGAGvB,OAAOhB,EAGTouB,cAAcxY,GACZ,QAAsB,IAAX2Y,GACT,MAAM,IAAIxtB,UAAU,gEAGtB,IAAIytB,EAAmB/mB,KAAK2D,SAEG,WAA3B3D,KAAK+J,QAAQiI,UACf+U,EAAmB5Y,EACVpW,EAAUiI,KAAK+J,QAAQiI,WAChC+U,EAAmB5uB,EAAW6H,KAAK+J,QAAQiI,WACA,iBAA3BhS,KAAK+J,QAAQiI,YAC7B+U,EAAmB/mB,KAAK+J,QAAQiI,WAGlC,MAAMkU,EAAelmB,KAAKgnB,mBACpBC,EAAkBf,EAAapD,UAAU5b,KAAKkd,GAA8B,gBAAlBA,EAAS5oB,OAA+C,IAArB4oB,EAASnT,SAE5GjR,KAAKqmB,QAAUS,GAAoBC,EAAkB/mB,KAAKsmB,MAAOJ,GAE7De,GACFrhB,EAAYC,iBAAiB7F,KAAKsmB,MAAO,SAAU,UAIvDxX,SAAS9X,EAAUgJ,KAAK2D,UACtB,OAAO3M,EAAQ+C,UAAUC,SAnNL,QAsNtBusB,kBACE,OAAOtf,EAAec,KAAK/H,KAAK2D,SAhNd,kBAgNuC,GAG3DujB,gBACE,MAAMC,EAAiBnnB,KAAK2D,SAASlJ,WAErC,GAAI0sB,EAAeptB,UAAUC,SA3NN,WA4NrB,OAAO+rB,GAGT,GAAIoB,EAAeptB,UAAUC,SA9NJ,aA+NvB,OAAOgsB,GAIT,MAAMoB,EAAkF,QAA1E1tB,iBAAiBsG,KAAKsmB,OAAO3sB,iBAAiB,iBAAiBpC,OAE7E,OAAI4vB,EAAeptB,UAAUC,SAvOP,UAwObotB,EAAQxB,GAAmBD,GAG7ByB,EAAQtB,GAAsBD,GAGvCY,gBACE,OAA0D,OAAnDzmB,KAAK2D,SAASiB,QAAS,WAGhCyiB,aACE,MAAM9gB,OAAEA,GAAWvG,KAAK+J,QAExB,MAAsB,iBAAXxD,EACFA,EAAOjP,MAAM,KAAK6Q,IAAI3C,GAAO9I,OAAOqQ,SAASvH,EAAK,KAGrC,mBAAXe,EACF+gB,GAAc/gB,EAAO+gB,EAAYtnB,KAAK2D,UAGxC4C,EAGTygB,mBACE,MAAMO,EAAwB,CAC5BnX,UAAWpQ,KAAKknB,gBAChBpE,UAAW,CAAC,CACVtnB,KAAM,kBACNoW,QAAS,CACPuJ,SAAUnb,KAAK+J,QAAQoR,WAG3B,CACE3f,KAAM,SACNoW,QAAS,CACPrL,OAAQvG,KAAKqnB,iBAanB,MAP6B,WAAzBrnB,KAAK+J,QAAQkc,UACfsB,EAAsBzE,UAAY,CAAC,CACjCtnB,KAAM,cACNyV,SAAS,KAIN,IACFsW,KACsC,mBAA9BvnB,KAAK+J,QAAQmc,aAA8BlmB,KAAK+J,QAAQmc,aAAaqB,GAAyBvnB,KAAK+J,QAAQmc,cAI1HsB,iBAAgBhlB,IAAEA,EAAFxF,OAAOA,IACrB,MAAMyqB,EAAQxgB,EAAeC,KAxRF,8DAwR+BlH,KAAKsmB,OAAOngB,OAAO3M,GAExEiuB,EAAMrvB,QAMX+E,EAAqBsqB,EAAOzqB,EAtTT,cAsTiBwF,GAAyBilB,EAAMrwB,SAAS4F,IAAS4pB,QAKjEziB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOkhB,GAASvhB,oBAAoB7E,KAAMzH,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,SAIQ4L,kBAACjF,GAChB,GAAIA,IA3UmB,IA2UTA,EAAMyG,QAAiD,UAAfzG,EAAMsB,MA9UhD,QA8UoEtB,EAAMsD,KACpF,OAGF,MAAMklB,EAAUzgB,EAAeC,KA7TN,+BA+TzB,IAAK,IAAIlI,EAAI,EAAGC,EAAMyoB,EAAQtvB,OAAQ4G,EAAIC,EAAKD,IAAK,CAClD,MAAM2oB,EAAUvB,GAAShiB,YAAYsjB,EAAQ1oB,IAC7C,IAAK2oB,IAAyC,IAA9BA,EAAQ5d,QAAQoc,UAC9B,SAGF,IAAKwB,EAAQ7Y,WACX,SAGF,MAAMhP,EAAgB,CACpBA,cAAe6nB,EAAQhkB,UAGzB,GAAIzE,EAAO,CACT,MAAM0oB,EAAe1oB,EAAM0oB,eACrBC,EAAeD,EAAaxwB,SAASuwB,EAAQrB,OACnD,GACEsB,EAAaxwB,SAASuwB,EAAQhkB,WACC,WAA9BgkB,EAAQ5d,QAAQoc,YAA2B0B,GACb,YAA9BF,EAAQ5d,QAAQoc,WAA2B0B,EAE5C,SAIF,GAAIF,EAAQrB,MAAMtsB,SAASkF,EAAMlC,UAA4B,UAAfkC,EAAMsB,MA9W5C,QA8WgEtB,EAAMsD,KAAoB,qCAAqCnJ,KAAK6F,EAAMlC,OAAO2H,UACvJ,SAGiB,UAAfzF,EAAMsB,OACRV,EAAc4E,WAAaxF,GAI/ByoB,EAAQd,cAAc/mB,IAICqE,4BAACnN,GAC1B,OAAOW,EAAuBX,IAAYA,EAAQyD,WAGxB0J,6BAACjF,GAQ3B,GAAI,kBAAkB7F,KAAK6F,EAAMlC,OAAO2H,SAxY1B,UAyYZzF,EAAMsD,KA1YO,WA0YetD,EAAMsD,MAtYjB,cAuYftD,EAAMsD,KAxYO,YAwYmBtD,EAAMsD,KACtCtD,EAAMlC,OAAO4H,QApXC,oBAqXf8gB,GAAersB,KAAK6F,EAAMsD,KAC3B,OAGF,MAAMslB,EAAW9nB,KAAKjG,UAAUC,SAhYZ,QAkYpB,IAAK8tB,GAnZU,WAmZE5oB,EAAMsD,IACrB,OAMF,GAHAtD,EAAMyD,iBACNzD,EAAM6oB,kBAEFnuB,EAAWoG,MACb,OAGF,MAAMgoB,EAAkBhoB,KAAKyH,QAvYJ,+BAuYoCzH,KAAOiH,EAAeW,KAAK5H,KAvY/D,+BAuY2F,GAC9GgD,EAAWojB,GAASvhB,oBAAoBmjB,GAE9C,GAjae,WAiaX9oB,EAAMsD,IAKV,MAnaiB,YAmabtD,EAAMsD,KAlaS,cAkaetD,EAAMsD,KACjCslB,GACH9kB,EAASgM,YAGXhM,EAASwkB,gBAAgBtoB,SAItB4oB,GA9aS,UA8aG5oB,EAAMsD,KACrB4jB,GAAS6B,cAdTjlB,EAAS+L,QAyBfzO,EAAaQ,GAAGrJ,SA7agB,+BASH,8BAoa2C2uB,GAAS8B,uBACjF5nB,EAAaQ,GAAGrJ,SA9agB,+BAUV,iBAoa2C2uB,GAAS8B,uBAC1E5nB,EAAaQ,GAAGrJ,SAhbc,6BAgbkB2uB,GAAS6B,YACzD3nB,EAAaQ,GAAGrJ,SA/ac,6BA+akB2uB,GAAS6B,YACzD3nB,EAAaQ,GAAGrJ,SAlbc,6BAUD,+BAwayC,SAAUyH,GAC9EA,EAAMyD,iBACNyjB,GAASvhB,oBAAoB7E,MAAMqF,YAUrCjK,EAAmBgrB,IClenB,MAAM+B,GACJzkB,cACE1D,KAAK2D,SAAWlM,SAASuD,KAG3BotB,WAEE,MAAMC,EAAgB5wB,SAAS2C,gBAAgByb,YAC/C,OAAOlY,KAAK4N,IAAIxQ,OAAOutB,WAAaD,GAGtCtZ,OACE,MAAM2D,EAAQ1S,KAAKooB,WACnBpoB,KAAKuoB,mBAELvoB,KAAKwoB,sBAAsBxoB,KAAK2D,SAAU,eAAgB8kB,GAAmBA,EAAkB/V,GAE/F1S,KAAKwoB,sBApBsB,oDAoBwB,eAAgBC,GAAmBA,EAAkB/V,GACxG1S,KAAKwoB,sBApBuB,cAoBwB,cAAeC,GAAmBA,EAAkB/V,GAG1G6V,mBACEvoB,KAAK0oB,sBAAsB1oB,KAAK2D,SAAU,YAC1C3D,KAAK2D,SAAS6L,MAAM8J,SAAW,SAGjCkP,sBAAsBvxB,EAAU0xB,EAAWrtB,GACzC,MAAMstB,EAAiB5oB,KAAKooB,WAW5BpoB,KAAK6oB,2BAA2B5xB,EAVHD,IAC3B,GAAIA,IAAYgJ,KAAK2D,UAAY5I,OAAOutB,WAAatxB,EAAQ6e,YAAc+S,EACzE,OAGF5oB,KAAK0oB,sBAAsB1xB,EAAS2xB,GACpC,MAAMF,EAAkB1tB,OAAOrB,iBAAiB1C,GAAS2xB,GACzD3xB,EAAQwY,MAAMmZ,GAAgBrtB,EAASoB,OAAOC,WAAW8rB,IAA7B,OAMhCvJ,QACElf,KAAK8oB,wBAAwB9oB,KAAK2D,SAAU,YAC5C3D,KAAK8oB,wBAAwB9oB,KAAK2D,SAAU,gBAC5C3D,KAAK8oB,wBA/CsB,oDA+C0B,gBACrD9oB,KAAK8oB,wBA/CuB,cA+C0B,eAGxDJ,sBAAsB1xB,EAAS2xB,GAC7B,MAAMI,EAAc/xB,EAAQwY,MAAMmZ,GAC9BI,GACFnjB,EAAYC,iBAAiB7O,EAAS2xB,EAAWI,GAIrDD,wBAAwB7xB,EAAU0xB,GAWhC3oB,KAAK6oB,2BAA2B5xB,EAVHD,IAC3B,MAAM8B,EAAQ8M,EAAYU,iBAAiBtP,EAAS2xB,QAC/B,IAAV7vB,EACT9B,EAAQwY,MAAMwZ,eAAeL,IAE7B/iB,EAAYE,oBAAoB9O,EAAS2xB,GACzC3xB,EAAQwY,MAAMmZ,GAAa7vB,KAOjC+vB,2BAA2B5xB,EAAUgyB,GAC/BlxB,EAAUd,GACZgyB,EAAShyB,GAETgQ,EAAeC,KAAKjQ,EAAU+I,KAAK2D,UAAUhL,QAAQswB,GAIzDC,gBACE,OAAOlpB,KAAKooB,WAAa,GClF7B,MAAM9f,GAAU,CACd6gB,UAAW,iBACX3vB,WAAW,EACX0K,YAAY,EACZklB,YAAa,OACbC,cAAe,MAGXxgB,GAAc,CAClBsgB,UAAW,SACX3vB,UAAW,UACX0K,WAAY,UACZklB,YAAa,mBACbC,cAAe,mBAQjB,MAAMC,GACJ5lB,YAAYnL,GACVyH,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKupB,aAAc,EACnBvpB,KAAK2D,SAAW,KAGlBqL,KAAK1T,GACE0E,KAAK+J,QAAQvQ,WAKlBwG,KAAKwpB,UAEDxpB,KAAK+J,QAAQ7F,YACfvJ,EAAOqF,KAAKypB,eAGdzpB,KAAKypB,cAAc1vB,UAAUsS,IAvBT,QAyBpBrM,KAAK0pB,kBAAkB,KACrBxtB,EAAQZ,MAbRY,EAAQZ,GAiBZyT,KAAKzT,GACE0E,KAAK+J,QAAQvQ,WAKlBwG,KAAKypB,cAAc1vB,UAAUwJ,OApCT,QAsCpBvD,KAAK0pB,kBAAkB,KACrB1pB,KAAK6D,UACL3H,EAAQZ,MARRY,EAAQZ,GAcZmuB,cACE,IAAKzpB,KAAK2D,SAAU,CAClB,MAAMgmB,EAAWlyB,SAASmyB,cAAc,OACxCD,EAASR,UAAYnpB,KAAK+J,QAAQof,UAC9BnpB,KAAK+J,QAAQ7F,YACfylB,EAAS5vB,UAAUsS,IApDH,QAuDlBrM,KAAK2D,SAAWgmB,EAGlB,OAAO3pB,KAAK2D,SAGdqG,WAAWzR,GAST,OARAA,EAAS,IACJ+P,MACmB,iBAAX/P,EAAsBA,EAAS,KAIrC6wB,YAAcjxB,EAAWI,EAAO6wB,aACvC/wB,EAtES,WAsEaE,EAAQsQ,IACvBtQ,EAGTixB,UACMxpB,KAAKupB,cAITvpB,KAAK+J,QAAQqf,YAAYS,OAAO7pB,KAAKypB,eAErCnpB,EAAaQ,GAAGd,KAAKypB,cA7EA,wBA6EgC,KACnDvtB,EAAQ8D,KAAK+J,QAAQsf,iBAGvBrpB,KAAKupB,aAAc,GAGrB1lB,UACO7D,KAAKupB,cAIVjpB,EAAaC,IAAIP,KAAK2D,SAzFD,yBA2FrB3D,KAAK2D,SAASJ,SACdvD,KAAKupB,aAAc,GAGrBG,kBAAkBpuB,GAChBa,EAAuBb,EAAU0E,KAAKypB,cAAezpB,KAAK+J,QAAQ7F,aClHtE,MAAMoE,GAAU,CACdwhB,YAAa,KACbC,WAAW,GAGPlhB,GAAc,CAClBihB,YAAa,UACbC,UAAW,WAab,MAAMC,GACJtmB,YAAYnL,GACVyH,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKiqB,WAAY,EACjBjqB,KAAKkqB,qBAAuB,KAG9BC,WACE,MAAML,YAAEA,EAAFC,UAAeA,GAAc/pB,KAAK+J,QAEpC/J,KAAKiqB,YAILF,GACFD,EAAYlD,QAGdtmB,EAAaC,IAAI9I,SA1BF,iBA2Bf6I,EAAaQ,GAAGrJ,SA1BG,uBA0BsByH,GAASc,KAAKoqB,eAAelrB,IACtEoB,EAAaQ,GAAGrJ,SA1BO,2BA0BsByH,GAASc,KAAKqqB,eAAenrB,IAE1Ec,KAAKiqB,WAAY,GAGnBK,aACOtqB,KAAKiqB,YAIVjqB,KAAKiqB,WAAY,EACjB3pB,EAAaC,IAAI9I,SAvCF,kBA4CjB2yB,eAAelrB,GACb,MAAMlC,OAAEA,GAAWkC,GACb4qB,YAAEA,GAAgB9pB,KAAK+J,QAE7B,GACE/M,IAAWvF,UACXuF,IAAW8sB,GACXA,EAAY9vB,SAASgD,GAErB,OAGF,MAAMqU,EAAWpK,EAAegB,kBAAkB6hB,GAE1B,IAApBzY,EAASjZ,OACX0xB,EAAYlD,QArDO,aAsDV5mB,KAAKkqB,qBACd7Y,EAASA,EAASjZ,OAAS,GAAGwuB,QAE9BvV,EAAS,GAAGuV,QAIhByD,eAAenrB,GA/DD,QAgERA,EAAMsD,MAIVxC,KAAKkqB,qBAAuBhrB,EAAMqrB,SAlEb,WADD,WAsEtBvgB,WAAWzR,GAMT,OALAA,EAAS,IACJ+P,MACmB,iBAAX/P,EAAsBA,EAAS,IAE5CF,EAlFS,YAkFaE,EAAQsQ,IACvBtQ,GC1EX,MAMM+P,GAAU,CACdqhB,UAAU,EACVnhB,UAAU,EACVoe,OAAO,GAGH/d,GAAc,CAClB8gB,SAAU,mBACVnhB,SAAU,UACVoe,MAAO,WA+BT,MAAM4D,WAAc/mB,EAClBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKyqB,QAAUxjB,EAAeK,QAfV,gBAemCtH,KAAK2D,UAC5D3D,KAAK0qB,UAAY1qB,KAAK2qB,sBACtB3qB,KAAK4qB,WAAa5qB,KAAK6qB,uBACvB7qB,KAAK8O,UAAW,EAChB9O,KAAK8qB,sBAAuB,EAC5B9qB,KAAKqO,kBAAmB,EACxBrO,KAAK+qB,WAAa,IAAI5C,GAKN7f,qBAChB,OAAOA,GAGM7M,kBACb,MAnES,QAwEX4J,OAAOvF,GACL,OAAOE,KAAK8O,SAAW9O,KAAK+O,OAAS/O,KAAKgP,KAAKlP,GAGjDkP,KAAKlP,GACCE,KAAK8O,UAAY9O,KAAKqO,kBAIR/N,EAAamB,QAAQzB,KAAK2D,SA5D5B,gBA4DkD,CAChE7D,cAAAA,IAGYiC,mBAId/B,KAAK8O,UAAW,EAEZ9O,KAAKgrB,gBACPhrB,KAAKqO,kBAAmB,GAG1BrO,KAAK+qB,WAAWhc,OAEhBtX,SAASuD,KAAKjB,UAAUsS,IAnEJ,cAqEpBrM,KAAKirB,gBAELjrB,KAAKkrB,kBACLlrB,KAAKmrB,kBAEL7qB,EAAaQ,GAAGd,KAAKyqB,QA7EQ,6BA6E0B,KACrDnqB,EAAaS,IAAIf,KAAK2D,SA/EG,2BA+E8BzE,IACjDA,EAAMlC,SAAWgD,KAAK2D,WACxB3D,KAAK8qB,sBAAuB,OAKlC9qB,KAAKorB,cAAc,IAAMprB,KAAKqrB,aAAavrB,KAG7CiP,OACE,IAAK/O,KAAK8O,UAAY9O,KAAKqO,iBACzB,OAKF,GAFkB/N,EAAamB,QAAQzB,KAAK2D,SAtG5B,iBAwGF5B,iBACZ,OAGF/B,KAAK8O,UAAW,EAChB,MAAM5K,EAAalE,KAAKgrB,cAEpB9mB,IACFlE,KAAKqO,kBAAmB,GAG1BrO,KAAKkrB,kBACLlrB,KAAKmrB,kBAELnrB,KAAK4qB,WAAWN,aAEhBtqB,KAAK2D,SAAS5J,UAAUwJ,OA1GJ,QA4GpBjD,EAAaC,IAAIP,KAAK2D,SApHG,0BAqHzBrD,EAAaC,IAAIP,KAAKyqB,QAlHO,8BAoH7BzqB,KAAKiE,eAAe,IAAMjE,KAAKsrB,aAActrB,KAAK2D,SAAUO,GAG9DL,UACE,CAAC9I,OAAQiF,KAAKyqB,SACX9xB,QAAQ4yB,GAAejrB,EAAaC,IAAIgrB,EAlJ5B,cAoJfvrB,KAAK0qB,UAAU7mB,UACf7D,KAAK4qB,WAAWN,aAChBhhB,MAAMzF,UAGR2nB,eACExrB,KAAKirB,gBAKPN,sBACE,OAAO,IAAIrB,GAAS,CAClB9vB,UAAWqH,QAAQb,KAAK+J,QAAQ4f,UAChCzlB,WAAYlE,KAAKgrB,gBAIrBH,uBACE,OAAO,IAAIb,GAAU,CACnBF,YAAa9pB,KAAK2D,WAItBqG,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EApLS,QAoLaE,EAAQsQ,IACvBtQ,EAGT8yB,aAAavrB,GACX,MAAMoE,EAAalE,KAAKgrB,cAClBS,EAAYxkB,EAAeK,QArJT,cAqJsCtH,KAAKyqB,SAE9DzqB,KAAK2D,SAASlJ,YAAcuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAE1ErC,SAASuD,KAAK6uB,OAAO7pB,KAAK2D,UAG5B3D,KAAK2D,SAAS6L,MAAMyW,QAAU,QAC9BjmB,KAAK2D,SAASoC,gBAAgB,eAC9B/F,KAAK2D,SAAS2B,aAAa,cAAc,GACzCtF,KAAK2D,SAAS2B,aAAa,OAAQ,UACnCtF,KAAK2D,SAASuV,UAAY,EAEtBuS,IACFA,EAAUvS,UAAY,GAGpBhV,GACFvJ,EAAOqF,KAAK2D,UAGd3D,KAAK2D,SAAS5J,UAAUsS,IA/KJ,QA4LpBrM,KAAKiE,eAXsB,KACrBjE,KAAK+J,QAAQ6c,OACf5mB,KAAK4qB,WAAWT,WAGlBnqB,KAAKqO,kBAAmB,EACxB/N,EAAamB,QAAQzB,KAAK2D,SAjMX,iBAiMkC,CAC/C7D,cAAAA,KAIoCE,KAAKyqB,QAASvmB,GAGxDgnB,kBACMlrB,KAAK8O,SACPxO,EAAaQ,GAAGd,KAAK2D,SAxMI,2BAwM6BzE,IAChDc,KAAK+J,QAAQvB,UA9NN,WA8NkBtJ,EAAMsD,KACjCtD,EAAMyD,iBACN3C,KAAK+O,QACK/O,KAAK+J,QAAQvB,UAjOd,WAiO0BtJ,EAAMsD,KACzCxC,KAAK0rB,+BAITprB,EAAaC,IAAIP,KAAK2D,SAjNG,4BAqN7BwnB,kBACMnrB,KAAK8O,SACPxO,EAAaQ,GAAG/F,OAzNA,kBAyNsB,IAAMiF,KAAKirB,iBAEjD3qB,EAAaC,IAAIxF,OA3ND,mBA+NpBuwB,aACEtrB,KAAK2D,SAAS6L,MAAMyW,QAAU,OAC9BjmB,KAAK2D,SAAS2B,aAAa,eAAe,GAC1CtF,KAAK2D,SAASoC,gBAAgB,cAC9B/F,KAAK2D,SAASoC,gBAAgB,QAC9B/F,KAAKqO,kBAAmB,EACxBrO,KAAK0qB,UAAU3b,KAAK,KAClBtX,SAASuD,KAAKjB,UAAUwJ,OA/NN,cAgOlBvD,KAAK2rB,oBACL3rB,KAAK+qB,WAAW7L,QAChB5e,EAAamB,QAAQzB,KAAK2D,SA5OV,qBAgPpBynB,cAAc9vB,GACZgF,EAAaQ,GAAGd,KAAK2D,SA7OI,yBA6O2BzE,IAC9Cc,KAAK8qB,qBACP9qB,KAAK8qB,sBAAuB,EAI1B5rB,EAAMlC,SAAWkC,EAAM0sB,iBAIG,IAA1B5rB,KAAK+J,QAAQ4f,SACf3pB,KAAK+O,OAC8B,WAA1B/O,KAAK+J,QAAQ4f,UACtB3pB,KAAK0rB,gCAIT1rB,KAAK0qB,UAAU1b,KAAK1T,GAGtB0vB,cACE,OAAOhrB,KAAK2D,SAAS5J,UAAUC,SA3PX,QA8PtB0xB,6BAEE,GADkBprB,EAAamB,QAAQzB,KAAK2D,SA3QlB,0BA4QZ5B,iBACZ,OAGF,MAAMhI,UAAEA,EAAF0gB,aAAaA,EAAbjL,MAA2BA,GAAUxP,KAAK2D,SAC1CkoB,EAAqBpR,EAAehjB,SAAS2C,gBAAgBwb,cAG7DiW,GAA0C,WAApBrc,EAAMgK,WAA2Bzf,EAAUC,SAtQjD,kBA0QjB6xB,IACHrc,EAAMgK,UAAY,UAGpBzf,EAAUsS,IA9QY,gBA+QtBrM,KAAKiE,eAAe,KAClBlK,EAAUwJ,OAhRU,gBAiRfsoB,GACH7rB,KAAKiE,eAAe,KAClBuL,EAAMgK,UAAY,IACjBxZ,KAAKyqB,UAETzqB,KAAKyqB,SAERzqB,KAAK2D,SAASijB,SAOhBqE,gBACE,MAAMY,EAAqB7rB,KAAK2D,SAAS8W,aAAehjB,SAAS2C,gBAAgBwb,aAC3EgT,EAAiB5oB,KAAK+qB,WAAW3C,WACjC0D,EAAoBlD,EAAiB,IAErCkD,GAAqBD,IAAuB3wB,KAAa4wB,IAAsBD,GAAsB3wB,OACzG8E,KAAK2D,SAAS6L,MAAMuc,YAAiBnD,EAAF,OAGhCkD,IAAsBD,IAAuB3wB,MAAc4wB,GAAqBD,GAAsB3wB,OACzG8E,KAAK2D,SAAS6L,MAAMwc,aAAkBpD,EAAF,MAIxC+C,oBACE3rB,KAAK2D,SAAS6L,MAAMuc,YAAc,GAClC/rB,KAAK2D,SAAS6L,MAAMwc,aAAe,GAKf7nB,uBAAC5L,EAAQuH,GAC7B,OAAOE,KAAKiF,MAAK,WACf,MAAMC,EAAOslB,GAAM3lB,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQuH,QAWnBQ,EAAaQ,GAAGrJ,SAhVc,0BAUD,4BAsUyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAElC,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGRrC,EAAaS,IAAI/D,EA9VC,gBA8VmBivB,IAC/BA,EAAUlqB,kBAKdzB,EAAaS,IAAI/D,EArWC,kBAqWqB,KACjCxD,EAAUwG,OACZA,KAAK4mB,YAMX,MAAMsF,EAAejlB,EAAeK,QA9VhB,eA+VhB4kB,GACF1B,GAAMpmB,YAAY8nB,GAAcnd,OAGrByb,GAAM3lB,oBAAoB7H,GAElCqI,OAAOrF,SAGduE,EAAqBimB,IASrBpvB,EAAmBovB,ICrZnB,MAOMliB,GAAU,CACdqhB,UAAU,EACVnhB,UAAU,EACV8P,QAAQ,GAGJzP,GAAc,CAClB8gB,SAAU,UACVnhB,SAAU,UACV8P,OAAQ,WAsBV,MAAM6T,WAAkB1oB,EACtBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAK8O,UAAW,EAChB9O,KAAK0qB,UAAY1qB,KAAK2qB,sBACtB3qB,KAAK4qB,WAAa5qB,KAAK6qB,uBACvB7qB,KAAKuK,qBAKQ9O,kBACb,MApDS,YAuDO6M,qBAChB,OAAOA,GAKTjD,OAAOvF,GACL,OAAOE,KAAK8O,SAAW9O,KAAK+O,OAAS/O,KAAKgP,KAAKlP,GAGjDkP,KAAKlP,GACCE,KAAK8O,UAISxO,EAAamB,QAAQzB,KAAK2D,SA/C5B,oBA+CkD,CAAE7D,cAAAA,IAEtDiC,mBAId/B,KAAK8O,UAAW,EAChB9O,KAAK2D,SAAS6L,MAAM4c,WAAa,UAEjCpsB,KAAK0qB,UAAU1b,OAEVhP,KAAK+J,QAAQuO,SAChB,IAAI6P,IAAkBpZ,OAGxB/O,KAAK2D,SAASoC,gBAAgB,eAC9B/F,KAAK2D,SAAS2B,aAAa,cAAc,GACzCtF,KAAK2D,SAAS2B,aAAa,OAAQ,UACnCtF,KAAK2D,SAAS5J,UAAUsS,IArEJ,QA+EpBrM,KAAKiE,eARoB,KAClBjE,KAAK+J,QAAQuO,QAChBtY,KAAK4qB,WAAWT,WAGlB7pB,EAAamB,QAAQzB,KAAK2D,SAvEX,qBAuEkC,CAAE7D,cAAAA,KAGfE,KAAK2D,UAAU,IAGvDoL,OACO/O,KAAK8O,WAIQxO,EAAamB,QAAQzB,KAAK2D,SAjF5B,qBAmFF5B,mBAId/B,KAAK4qB,WAAWN,aAChBtqB,KAAK2D,SAAS0oB,OACdrsB,KAAK8O,UAAW,EAChB9O,KAAK2D,SAAS5J,UAAUwJ,OAhGJ,QAiGpBvD,KAAK0qB,UAAU3b,OAef/O,KAAKiE,eAboB,KACvBjE,KAAK2D,SAAS2B,aAAa,eAAe,GAC1CtF,KAAK2D,SAASoC,gBAAgB,cAC9B/F,KAAK2D,SAASoC,gBAAgB,QAC9B/F,KAAK2D,SAAS6L,MAAM4c,WAAa,SAE5BpsB,KAAK+J,QAAQuO,SAChB,IAAI6P,IAAkBjJ,QAGxB5e,EAAamB,QAAQzB,KAAK2D,SAtGV,wBAyGoB3D,KAAK2D,UAAU,KAGvDE,UACE7D,KAAK0qB,UAAU7mB,UACf7D,KAAK4qB,WAAWN,aAChBhhB,MAAMzF,UAKRmG,WAAWzR,GAOT,OANAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,EAAsBA,EAAS,IAE5CF,EApJS,YAoJaE,EAAQsQ,IACvBtQ,EAGToyB,sBACE,OAAO,IAAIrB,GAAS,CAClBH,UAtIsB,qBAuItB3vB,UAAWwG,KAAK+J,QAAQ4f,SACxBzlB,YAAY,EACZklB,YAAappB,KAAK2D,SAASlJ,WAC3B4uB,cAAe,IAAMrpB,KAAK+O,SAI9B8b,uBACE,OAAO,IAAIb,GAAU,CACnBF,YAAa9pB,KAAK2D,WAItB4G,qBACEjK,EAAaQ,GAAGd,KAAK2D,SA7IM,+BA6I2BzE,IAChDc,KAAK+J,QAAQvB,UArKJ,WAqKgBtJ,EAAMsD,KACjCxC,KAAK+O,SAOW5K,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOinB,GAAUtnB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqB4M,IAAjBD,EAAK3M,IAAyBA,EAAOlB,WAAW,MAAmB,gBAAXkB,EAC1D,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,WAWnBM,EAAaQ,GAAGrJ,SA9Kc,8BAGD,gCA2KyC,SAAUyH,GAC9E,MAAMlC,EAASrF,EAAuBqI,MAMtC,GAJI,CAAC,IAAK,QAAQ5I,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,MACb,OAGFM,EAAaS,IAAI/D,EA1LG,sBA0LmB,KAEjCxD,EAAUwG,OACZA,KAAK4mB,UAKT,MAAMsF,EAAejlB,EAAeK,QAvMhB,mBAwMhB4kB,GAAgBA,IAAiBlvB,GACnCmvB,GAAU/nB,YAAY8nB,GAAcnd,OAGzBod,GAAUtnB,oBAAoB7H,GACtCqI,OAAOrF,SAGdM,EAAaQ,GAAG/F,OAjOa,6BAiOgB,IAC3CkM,EAAeC,KAjNK,mBAiNevO,QAAQ0P,GAAM8jB,GAAUtnB,oBAAoBwD,GAAI2G,SAGrFzK,EAAqB4nB,IAOrB/wB,EAAmB+wB,ICtQnB,MAAMG,GAAW,IAAI9tB,IAAI,CACvB,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI+tB,GAAmB,6DAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAMC,KAC9B,MAAMC,EAAWF,EAAKlc,SAASrX,cAE/B,GAAIwzB,EAAqBv1B,SAASw1B,GAChC,OAAIN,GAAS5sB,IAAIktB,IACR/rB,QAAQ0rB,GAAiBlzB,KAAKqzB,EAAKG,YAAcL,GAAiBnzB,KAAKqzB,EAAKG,YAMvF,MAAMC,EAASH,EAAqBxmB,OAAO4mB,GAAaA,aAAqB3zB,QAG7E,IAAK,IAAI4F,EAAI,EAAGC,EAAM6tB,EAAO10B,OAAQ4G,EAAIC,EAAKD,IAC5C,GAAI8tB,EAAO9tB,GAAG3F,KAAKuzB,GACjB,OAAO,EAIX,OAAO,GAqCF,SAASI,GAAaC,EAAYC,EAAWC,GAClD,IAAKF,EAAW70B,OACd,OAAO60B,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAGpB,MACMG,GADY,IAAIryB,OAAOsyB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgB90B,OAAOC,KAAKw0B,GAC5B7b,EAAW,GAAGlK,UAAUimB,EAAgBpyB,KAAKqF,iBAAiB,MAEpE,IAAK,IAAIrB,EAAI,EAAGC,EAAMoS,EAASjZ,OAAQ4G,EAAIC,EAAKD,IAAK,CACnD,MAAMqJ,EAAKgJ,EAASrS,GACdwuB,EAASnlB,EAAGmI,SAASrX,cAE3B,IAAKo0B,EAAcn2B,SAASo2B,GAAS,CACnCnlB,EAAG9E,SAEH,SAGF,MAAMkqB,EAAgB,GAAGtmB,UAAUkB,EAAGpC,YAChCynB,EAAoB,GAAGvmB,OAAO+lB,EAAU,MAAQ,GAAIA,EAAUM,IAAW,IAE/EC,EAAc90B,QAAQ+zB,IACfD,GAAiBC,EAAMgB,IAC1BrlB,EAAGtC,gBAAgB2mB,EAAKlc,YAK9B,OAAO4c,EAAgBpyB,KAAK2yB,UC7F9B,MAIMC,GAAwB,IAAIpvB,IAAI,CAAC,WAAY,YAAa,eAE1DqK,GAAc,CAClBglB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPtsB,QAAS,SACTusB,MAAO,kBACP9T,KAAM,UACNjjB,SAAU,mBACVmZ,UAAW,oBACX7J,OAAQ,0BACR4I,UAAW,2BACX2O,mBAAoB,QACpB3C,SAAU,mBACV8S,YAAa,oBACbC,SAAU,UACVf,WAAY,kBACZD,UAAW,SACXhH,aAAc,0BAGViI,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOpzB,IAAU,OAAS,QAC1BqzB,OAAQ,SACRC,KAAMtzB,IAAU,QAAU,QAGtBoN,GAAU,CACdulB,WAAW,EACXC,SAAU,+GAIVrsB,QAAS,cACTssB,MAAO,GACPC,MAAO,EACP9T,MAAM,EACNjjB,UAAU,EACVmZ,UAAW,MACX7J,OAAQ,CAAC,EAAG,GACZ4I,WAAW,EACX2O,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C3C,SAAU,kBACV8S,YAAa,GACbC,UAAU,EACVf,WAAY,KACZD,UD5B8B,CAE9BuB,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BrR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BsR,KAAM,GACNrR,EAAG,GACHsR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJtwB,EAAG,GACHuwB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,ICFJjK,aAAc,MAGVpuB,GAAQ,CACZs4B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBA0Bf,MAAMC,WAAgBrtB,EACpBC,YAAY1M,EAASuB,GACnB,QAAsB,IAAXuuB,GACT,MAAM,IAAIxtB,UAAU,+DAGtBgQ,MAAMtS,GAGNgJ,KAAK+wB,YAAa,EAClB/wB,KAAKgxB,SAAW,EAChBhxB,KAAKixB,YAAc,GACnBjxB,KAAKkxB,eAAiB,GACtBlxB,KAAKqmB,QAAU,KAGfrmB,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKmxB,IAAM,KAEXnxB,KAAKoxB,gBAKW9oB,qBAChB,OAAOA,GAGM7M,kBACb,MA1HS,UA6HK3D,mBACd,OAAOA,GAGa+Q,yBACpB,OAAOA,GAKTwoB,SACErxB,KAAK+wB,YAAa,EAGpBO,UACEtxB,KAAK+wB,YAAa,EAGpBQ,gBACEvxB,KAAK+wB,YAAc/wB,KAAK+wB,WAG1B1rB,OAAOnG,GACL,GAAKc,KAAK+wB,WAIV,GAAI7xB,EAAO,CACT,MAAMyoB,EAAU3nB,KAAKwxB,6BAA6BtyB,GAElDyoB,EAAQuJ,eAAeO,OAAS9J,EAAQuJ,eAAeO,MAEnD9J,EAAQ+J,uBACV/J,EAAQgK,OAAO,KAAMhK,GAErBA,EAAQiK,OAAO,KAAMjK,OAElB,CACL,GAAI3nB,KAAK6xB,gBAAgB93B,UAAUC,SA3FjB,QA6FhB,YADAgG,KAAK4xB,OAAO,KAAM5xB,MAIpBA,KAAK2xB,OAAO,KAAM3xB,OAItB6D,UACEqI,aAAalM,KAAKgxB,UAElB1wB,EAAaC,IAAIP,KAAK2D,SAASiB,QAjGX,UAEC,gBA+FqD5E,KAAK8xB,mBAE3E9xB,KAAKmxB,KACPnxB,KAAKmxB,IAAI5tB,SAGXvD,KAAK+xB,iBACLzoB,MAAMzF,UAGRmL,OACE,GAAoC,SAAhChP,KAAK2D,SAAS6L,MAAMyW,QACtB,MAAM,IAAI3hB,MAAM,uCAGlB,IAAMtE,KAAKgyB,kBAAmBhyB,KAAK+wB,WACjC,OAGF,MAAM9E,EAAY3rB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMw4B,MACvE2B,EAAa93B,EAAe6F,KAAK2D,UACjCuuB,EAA4B,OAAfD,EACjBjyB,KAAK2D,SAASgN,cAAcvW,gBAAgBJ,SAASgG,KAAK2D,UAC1DsuB,EAAWj4B,SAASgG,KAAK2D,UAE3B,GAAIsoB,EAAUlqB,mBAAqBmwB,EACjC,OAK4B,YAA1BlyB,KAAK0D,YAAYjI,MAAsBuE,KAAKmxB,KAAOnxB,KAAKmyB,aAAenyB,KAAKmxB,IAAIz5B,cAjIzD,kBAiI+Fi2B,YACxH3tB,KAAK+xB,iBACL/xB,KAAKmxB,IAAI5tB,SACTvD,KAAKmxB,IAAM,MAGb,MAAMA,EAAMnxB,KAAK6xB,gBACXO,EvE3NKC,CAAAA,IACb,GACEA,GAAU10B,KAAK20B,MArBH,IAqBS30B,KAAK40B,gBACnB96B,SAAS+6B,eAAeH,IAEjC,OAAOA,GuEsNSI,CAAOzyB,KAAK0D,YAAYjI,MAEtC01B,EAAI7rB,aAAa,KAAM8sB,GACvBpyB,KAAK2D,SAAS2B,aAAa,mBAAoB8sB,GAE3CpyB,KAAK+J,QAAQ8jB,WACfsD,EAAIp3B,UAAUsS,IArJI,QAwJpB,MAAM+D,EAA8C,mBAA3BpQ,KAAK+J,QAAQqG,UACpCpQ,KAAK+J,QAAQqG,UAAUnX,KAAK+G,KAAMmxB,EAAKnxB,KAAK2D,UAC5C3D,KAAK+J,QAAQqG,UAETsiB,EAAa1yB,KAAK2yB,eAAeviB,GACvCpQ,KAAK4yB,oBAAoBF,GAEzB,MAAMvjB,UAAEA,GAAcnP,KAAK+J,QAC3BjH,EAAKC,IAAIouB,EAAKnxB,KAAK0D,YAAYE,SAAU5D,MAEpCA,KAAK2D,SAASgN,cAAcvW,gBAAgBJ,SAASgG,KAAKmxB,OAC7DhiB,EAAU0a,OAAOsH,GACjB7wB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAM04B,WAGzDxwB,KAAKqmB,QACPrmB,KAAKqmB,QAAQ1N,SAEb3Y,KAAKqmB,QAAUS,GAAoB9mB,KAAK2D,SAAUwtB,EAAKnxB,KAAKgnB,iBAAiB0L,IAG/EvB,EAAIp3B,UAAUsS,IA3KM,QA6KpB,MAAM4hB,EAAcjuB,KAAK6yB,yBAAyB7yB,KAAK+J,QAAQkkB,aAC3DA,GACFkD,EAAIp3B,UAAUsS,OAAO4hB,EAAY32B,MAAM,MAOrC,iBAAkBG,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UAAU5O,QAAQ3B,IAC3CsJ,EAAaQ,GAAG9J,EAAS,YAAa0D,KAI1C,MAWMwJ,EAAalE,KAAKmxB,IAAIp3B,UAAUC,SAzMlB,QA0MpBgG,KAAKiE,eAZY,KACf,MAAM6uB,EAAiB9yB,KAAKixB,YAE5BjxB,KAAKixB,YAAc,KACnB3wB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMy4B,OA7LzC,QA+LduC,GACF9yB,KAAK4xB,OAAO,KAAM5xB,OAKQA,KAAKmxB,IAAKjtB,GAG1C6K,OACE,IAAK/O,KAAKqmB,QACR,OAGF,MAAM8K,EAAMnxB,KAAK6xB,gBAkBjB,GADkBvxB,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMs4B,MAC/DruB,iBACZ,OAGFovB,EAAIp3B,UAAUwJ,OAtOM,QA0OhB,iBAAkB9L,SAAS2C,iBAC7B,GAAG+M,UAAU1P,SAASuD,KAAKuM,UACxB5O,QAAQ3B,GAAWsJ,EAAaC,IAAIvJ,EAAS,YAAa0D,IAG/DsF,KAAKkxB,eAAL,OAAqC,EACrClxB,KAAKkxB,eAAL,OAAqC,EACrClxB,KAAKkxB,eAAL,OAAqC,EAErC,MAAMhtB,EAAalE,KAAKmxB,IAAIp3B,UAAUC,SArPlB,QAsPpBgG,KAAKiE,eAnCY,KACXjE,KAAK0xB,yBAhNU,SAoNf1xB,KAAKixB,aACPE,EAAI5tB,SAGNvD,KAAK+yB,iBACL/yB,KAAK2D,SAASoC,gBAAgB,oBAC9BzF,EAAamB,QAAQzB,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAMu4B,QAE3DrwB,KAAK+xB,mBAsBuB/xB,KAAKmxB,IAAKjtB,GACxClE,KAAKixB,YAAc,GAGrBtY,SACuB,OAAjB3Y,KAAKqmB,SACPrmB,KAAKqmB,QAAQ1N,SAMjBqZ,gBACE,OAAOnxB,QAAQb,KAAKmyB,YAGtBN,gBACE,GAAI7xB,KAAKmxB,IACP,OAAOnxB,KAAKmxB,IAGd,MAAMn6B,EAAUS,SAASmyB,cAAc,OACvC5yB,EAAQ22B,UAAY3tB,KAAK+J,QAAQ+jB,SAEjC,MAAMqD,EAAMn6B,EAAQuQ,SAAS,GAK7B,OAJAvH,KAAKgzB,WAAW7B,GAChBA,EAAIp3B,UAAUwJ,OAhRM,OAEA,QAgRpBvD,KAAKmxB,IAAMA,EACJnxB,KAAKmxB,IAGd6B,WAAW7B,GACTnxB,KAAKizB,uBAAuB9B,EAAKnxB,KAAKmyB,WAhRX,kBAmR7Bc,uBAAuBnF,EAAUoF,EAASj8B,GACxC,MAAMk8B,EAAkBlsB,EAAeK,QAAQrQ,EAAU62B,GAEpDoF,IAAWC,EAMhBnzB,KAAKozB,kBAAkBD,EAAiBD,GALtCC,EAAgB5vB,SAQpB6vB,kBAAkBp8B,EAASk8B,GACzB,GAAgB,OAAZl8B,EAIJ,OAAIe,EAAUm7B,IACZA,EAAU/6B,EAAW+6B,QAGjBlzB,KAAK+J,QAAQmQ,KACXgZ,EAAQz4B,aAAezD,IACzBA,EAAQ22B,UAAY,GACpB32B,EAAQ6yB,OAAOqJ,IAGjBl8B,EAAQq8B,YAAcH,EAAQG,mBAM9BrzB,KAAK+J,QAAQmQ,MACXla,KAAK+J,QAAQmkB,WACfgF,EAAUlG,GAAakG,EAASlzB,KAAK+J,QAAQmjB,UAAWltB,KAAK+J,QAAQojB,aAGvEn2B,EAAQ22B,UAAYuF,GAEpBl8B,EAAQq8B,YAAcH,GAI1Bf,WACE,MAAMpE,EAAQ/tB,KAAK2D,SAASzM,aAAa,2BAA6B8I,KAAK+J,QAAQgkB,MAEnF,OAAO/tB,KAAK6yB,yBAAyB9E,GAGvCuF,iBAAiBZ,GACf,MAAmB,UAAfA,EACK,MAGU,SAAfA,EACK,QAGFA,EAKTlB,6BAA6BtyB,EAAOyoB,GAClC,OAAOA,GAAW3nB,KAAK0D,YAAYmB,oBAAoB3F,EAAMa,eAAgBC,KAAKuzB,sBAGpFlM,aACE,MAAM9gB,OAAEA,GAAWvG,KAAK+J,QAExB,MAAsB,iBAAXxD,EACFA,EAAOjP,MAAM,KAAK6Q,IAAI3C,GAAO9I,OAAOqQ,SAASvH,EAAK,KAGrC,mBAAXe,EACF+gB,GAAc/gB,EAAO+gB,EAAYtnB,KAAK2D,UAGxC4C,EAGTssB,yBAAyBK,GACvB,MAA0B,mBAAZA,EAAyBA,EAAQj6B,KAAK+G,KAAK2D,UAAYuvB,EAGvElM,iBAAiB0L,GACf,MAAMnL,EAAwB,CAC5BnX,UAAWsiB,EACX5P,UAAW,CACT,CACEtnB,KAAM,OACNoW,QAAS,CACPkM,mBAAoB9d,KAAK+J,QAAQ+T,qBAGrC,CACEtiB,KAAM,SACNoW,QAAS,CACPrL,OAAQvG,KAAKqnB,eAGjB,CACE7rB,KAAM,kBACNoW,QAAS,CACPuJ,SAAUnb,KAAK+J,QAAQoR,WAG3B,CACE3f,KAAM,QACNoW,QAAS,CACP5a,QAAU,IAAGgJ,KAAK0D,YAAYjI,eAGlC,CACED,KAAM,WACNyV,SAAS,EACTC,MAAO,aACPvV,GAAIuJ,GAAQlF,KAAKwzB,6BAA6BtuB,KAGlDmgB,cAAengB,IACTA,EAAK0M,QAAQxB,YAAclL,EAAKkL,WAClCpQ,KAAKwzB,6BAA6BtuB,KAKxC,MAAO,IACFqiB,KACsC,mBAA9BvnB,KAAK+J,QAAQmc,aAA8BlmB,KAAK+J,QAAQmc,aAAaqB,GAAyBvnB,KAAK+J,QAAQmc,cAI1H0M,oBAAoBF,GAClB1yB,KAAK6xB,gBAAgB93B,UAAUsS,IAAK,GAAErM,KAAKyzB,0BAA0BzzB,KAAKszB,iBAAiBZ,MAG7FC,eAAeviB,GACb,OAAO+d,GAAc/d,EAAU7W,eAGjC63B,gBACmBpxB,KAAK+J,QAAQtI,QAAQnK,MAAM,KAEnCqB,QAAQ8I,IACf,GAAgB,UAAZA,EACFnB,EAAaQ,GAAGd,KAAK2D,SAAU3D,KAAK0D,YAAY5L,MAAM24B,MAAOzwB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAKqF,OAAOnG,SACpG,GA/ZU,WA+ZNuC,EAA4B,CACrC,MAAMiyB,EAnaQ,UAmaEjyB,EACdzB,KAAK0D,YAAY5L,MAAM84B,WACvB5wB,KAAK0D,YAAY5L,MAAM44B,QACnBiD,EAtaQ,UAsaGlyB,EACfzB,KAAK0D,YAAY5L,MAAM+4B,WACvB7wB,KAAK0D,YAAY5L,MAAM64B,SAEzBrwB,EAAaQ,GAAGd,KAAK2D,SAAU+vB,EAAS1zB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAK2xB,OAAOzyB,IACpFoB,EAAaQ,GAAGd,KAAK2D,SAAUgwB,EAAU3zB,KAAK+J,QAAQ9S,SAAUiI,GAASc,KAAK4xB,OAAO1yB,OAIzFc,KAAK8xB,kBAAoB,KACnB9xB,KAAK2D,UACP3D,KAAK+O,QAITzO,EAAaQ,GAAGd,KAAK2D,SAASiB,QAzbV,UAEC,gBAuboD5E,KAAK8xB,mBAE1E9xB,KAAK+J,QAAQ9S,SACf+I,KAAK+J,QAAU,IACV/J,KAAK+J,QACRtI,QAAS,SACTxK,SAAU,IAGZ+I,KAAK4zB,YAITA,YACE,MAAM7F,EAAQ/tB,KAAK2D,SAASzM,aAAa,SACnC28B,SAA2B7zB,KAAK2D,SAASzM,aAAa,2BAExD62B,GAA+B,WAAtB8F,KACX7zB,KAAK2D,SAAS2B,aAAa,yBAA0ByoB,GAAS,KAC1DA,GAAU/tB,KAAK2D,SAASzM,aAAa,eAAkB8I,KAAK2D,SAAS0vB,aACvErzB,KAAK2D,SAAS2B,aAAa,aAAcyoB,GAG3C/tB,KAAK2D,SAAS2B,aAAa,QAAS,KAIxCqsB,OAAOzyB,EAAOyoB,GACZA,EAAU3nB,KAAKwxB,6BAA6BtyB,EAAOyoB,GAE/CzoB,IACFyoB,EAAQuJ,eACS,YAAfhyB,EAAMsB,KApdQ,QADA,UAsdZ,GAGFmnB,EAAQkK,gBAAgB93B,UAAUC,SAnelB,SAEC,SAie8C2tB,EAAQsJ,YACzEtJ,EAAQsJ,YAleW,QAserB/kB,aAAayb,EAAQqJ,UAErBrJ,EAAQsJ,YAxea,OA0ehBtJ,EAAQ5d,QAAQikB,OAAUrG,EAAQ5d,QAAQikB,MAAMhf,KAKrD2Y,EAAQqJ,SAAW9zB,WAAW,KA/eT,SAgffyqB,EAAQsJ,aACVtJ,EAAQ3Y,QAET2Y,EAAQ5d,QAAQikB,MAAMhf,MARvB2Y,EAAQ3Y,QAWZ4iB,OAAO1yB,EAAOyoB,GACZA,EAAU3nB,KAAKwxB,6BAA6BtyB,EAAOyoB,GAE/CzoB,IACFyoB,EAAQuJ,eACS,aAAfhyB,EAAMsB,KAlfQ,QADA,SAofZmnB,EAAQhkB,SAAS3J,SAASkF,EAAMY,gBAGlC6nB,EAAQ+J,yBAIZxlB,aAAayb,EAAQqJ,UAErBrJ,EAAQsJ,YApgBY,MAsgBftJ,EAAQ5d,QAAQikB,OAAUrG,EAAQ5d,QAAQikB,MAAMjf,KAKrD4Y,EAAQqJ,SAAW9zB,WAAW,KA3gBV,QA4gBdyqB,EAAQsJ,aACVtJ,EAAQ5Y,QAET4Y,EAAQ5d,QAAQikB,MAAMjf,MARvB4Y,EAAQ5Y,QAWZ2iB,uBACE,IAAK,MAAMjwB,KAAWzB,KAAKkxB,eACzB,GAAIlxB,KAAKkxB,eAAezvB,GACtB,OAAO,EAIX,OAAO,EAGTuI,WAAWzR,GACT,MAAMu7B,EAAiBluB,EAAYI,kBAAkBhG,KAAK2D,UAqC1D,OAnCAlL,OAAOC,KAAKo7B,GAAgBn7B,QAAQo7B,IAC9BnG,GAAsBluB,IAAIq0B,WACrBD,EAAeC,MAI1Bx7B,EAAS,IACJyH,KAAK0D,YAAY4E,WACjBwrB,KACmB,iBAAXv7B,GAAuBA,EAASA,EAAS,KAG/C4W,WAAiC,IAArB5W,EAAO4W,UAAsB1X,SAASuD,KAAO7C,EAAWI,EAAO4W,WAEtD,iBAAjB5W,EAAOy1B,QAChBz1B,EAAOy1B,MAAQ,CACbhf,KAAMzW,EAAOy1B,MACbjf,KAAMxW,EAAOy1B,QAIW,iBAAjBz1B,EAAOw1B,QAChBx1B,EAAOw1B,MAAQx1B,EAAOw1B,MAAM/0B,YAGA,iBAAnBT,EAAO26B,UAChB36B,EAAO26B,QAAU36B,EAAO26B,QAAQl6B,YAGlCX,EAvoBS,UAuoBaE,EAAQyH,KAAK0D,YAAYmF,aAE3CtQ,EAAO21B,WACT31B,EAAOu1B,SAAWd,GAAaz0B,EAAOu1B,SAAUv1B,EAAO20B,UAAW30B,EAAO40B,aAGpE50B,EAGTg7B,qBACE,MAAMh7B,EAAS,GAEf,IAAK,MAAMiK,KAAOxC,KAAK+J,QACjB/J,KAAK0D,YAAY4E,QAAQ9F,KAASxC,KAAK+J,QAAQvH,KACjDjK,EAAOiK,GAAOxC,KAAK+J,QAAQvH,IAO/B,OAAOjK,EAGTw6B,iBACE,MAAM5B,EAAMnxB,KAAK6xB,gBACXmC,EAAwB,IAAI56B,OAAQ,UAAS4G,KAAKyzB,6BAA8B,KAChFQ,EAAW9C,EAAIj6B,aAAa,SAASgC,MAAM86B,GAChC,OAAbC,GAAqBA,EAAS77B,OAAS,GACzC67B,EAAS9rB,IAAI+rB,GAASA,EAAM38B,QACzBoB,QAAQw7B,GAAUhD,EAAIp3B,UAAUwJ,OAAO4wB,IAI9CV,uBACE,MAvqBiB,aA0qBnBD,6BAA6BlM,GAC3B,MAAMlW,MAAEA,GAAUkW,EAEblW,IAILpR,KAAKmxB,IAAM/f,EAAMC,SAASM,OAC1B3R,KAAK+yB,iBACL/yB,KAAK4yB,oBAAoB5yB,KAAK2yB,eAAevhB,EAAMhB,aAGrD2hB,iBACM/xB,KAAKqmB,UACPrmB,KAAKqmB,QAAQjB,UACbplB,KAAKqmB,QAAU,MAMGliB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO4rB,GAAQjsB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmB01B,ICxuBnB,MAKMxoB,GAAU,IACXwoB,GAAQxoB,QACX8H,UAAW,QACX7J,OAAQ,CAAC,EAAG,GACZ9E,QAAS,QACTyxB,QAAS,GACTpF,SAAU,+IAONjlB,GAAc,IACfioB,GAAQjoB,YACXqqB,QAAS,6BAGLp7B,GAAQ,CACZs4B,KAAO,kBACPC,OAAS,oBACTC,KAAO,kBACPC,MAAQ,mBACRC,SAAW,sBACXC,MAAQ,mBACRC,QAAU,qBACVC,SAAW,sBACXC,WAAa,wBACbC,WAAa,yBAYf,MAAMuD,WAAgBtD,GAGFxoB,qBAChB,OAAOA,GAGM7M,kBACb,MArDS,UAwDK3D,mBACd,OAAOA,GAGa+Q,yBACpB,OAAOA,GAKTmpB,gBACE,OAAOhyB,KAAKmyB,YAAcnyB,KAAKq0B,cAGjCrB,WAAW7B,GACTnxB,KAAKizB,uBAAuB9B,EAAKnxB,KAAKmyB,WAnCnB,mBAoCnBnyB,KAAKizB,uBAAuB9B,EAAKnxB,KAAKq0B,cAnCjB,iBAwCvBA,cACE,OAAOr0B,KAAK6yB,yBAAyB7yB,KAAK+J,QAAQmpB,SAGpDO,uBACE,MA/EiB,aAoFGtvB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOkvB,GAAQvvB,oBAAoB7E,KAAMzH,GAE/C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAab6C,EAAmBg5B,ICrGnB,MAKM9rB,GAAU,CACd/B,OAAQ,GACR9B,OAAQ,OACRzH,OAAQ,IAGJ6L,GAAc,CAClBtC,OAAQ,SACR9B,OAAQ,SACRzH,OAAQ,oBAeJs3B,GAAuB,8CAa7B,MAAMC,WAAkB9wB,EACtBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GACNgJ,KAAKw0B,eAA2C,SAA1Bx0B,KAAK2D,SAASgB,QAAqB5J,OAASiF,KAAK2D,SACvE3D,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKy0B,SAAW,GAChBz0B,KAAK00B,SAAW,GAChB10B,KAAK20B,cAAgB,KACrB30B,KAAK40B,cAAgB,EAErBt0B,EAAaQ,GAAGd,KAAKw0B,eAlCH,sBAkCiC,IAAMx0B,KAAK60B,YAE9D70B,KAAK80B,UACL90B,KAAK60B,WAKWvsB,qBAChB,OAAOA,GAGM7M,kBACb,MAjES,YAsEXq5B,UACE,MAAMC,EAAa/0B,KAAKw0B,iBAAmBx0B,KAAKw0B,eAAez5B,OAtC7C,SACE,WAyCdi6B,EAAuC,SAAxBh1B,KAAK+J,QAAQtF,OAChCswB,EACA/0B,KAAK+J,QAAQtF,OAETwwB,EA7Cc,aA6CDD,EACjBh1B,KAAKk1B,gBACL,EAEFl1B,KAAKy0B,SAAW,GAChBz0B,KAAK00B,SAAW,GAChB10B,KAAK40B,cAAgB50B,KAAKm1B,mBAEVluB,EAAeC,KAAKotB,GAAqBt0B,KAAK+J,QAAQ/M,QAE9DmL,IAAInR,IACV,MAAMo+B,EAAiB59B,EAAuBR,GACxCgG,EAASo4B,EAAiBnuB,EAAeK,QAAQ8tB,GAAkB,KAEzE,GAAIp4B,EAAQ,CACV,MAAMq4B,EAAYr4B,EAAOyJ,wBACzB,GAAI4uB,EAAU3iB,OAAS2iB,EAAU1iB,OAC/B,MAAO,CACL/M,EAAYovB,GAAch4B,GAAQ0J,IAAMuuB,EACxCG,GAKN,OAAO,OAENjvB,OAAOmvB,GAAQA,GACfnY,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IACxB1kB,QAAQ28B,IACPt1B,KAAKy0B,SAASx4B,KAAKq5B,EAAK,IACxBt1B,KAAK00B,SAASz4B,KAAKq5B,EAAK,MAI9BzxB,UACEvD,EAAaC,IAAIP,KAAKw0B,eAhHP,iBAiHflrB,MAAMzF,UAKRmG,WAAWzR,GAWT,OAVAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,KAG/CyE,OAAS7E,EAAWI,EAAOyE,SAAWvF,SAAS2C,gBAEtD/B,EAjIS,YAiIaE,EAAQsQ,IAEvBtQ,EAGT28B,gBACE,OAAOl1B,KAAKw0B,iBAAmBz5B,OAC7BiF,KAAKw0B,eAAe7tB,YACpB3G,KAAKw0B,eAAetb,UAGxBic,mBACE,OAAOn1B,KAAKw0B,eAAe/Z,cAAgB9c,KAAKC,IAC9CnG,SAASuD,KAAKyf,aACdhjB,SAAS2C,gBAAgBqgB,cAI7B8a,mBACE,OAAOv1B,KAAKw0B,iBAAmBz5B,OAC7BA,OAAOy6B,YACPx1B,KAAKw0B,eAAe/tB,wBAAwBkM,OAGhDkiB,WACE,MAAM3b,EAAYlZ,KAAKk1B,gBAAkBl1B,KAAK+J,QAAQxD,OAChDkU,EAAeza,KAAKm1B,mBACpBM,EAAYz1B,KAAK+J,QAAQxD,OAASkU,EAAeza,KAAKu1B,mBAM5D,GAJIv1B,KAAK40B,gBAAkBna,GACzBza,KAAK80B,UAGH5b,GAAauc,EAAjB,CACE,MAAMz4B,EAASgD,KAAK00B,SAAS10B,KAAK00B,SAASt8B,OAAS,GAEhD4H,KAAK20B,gBAAkB33B,GACzBgD,KAAK01B,UAAU14B,OAJnB,CAUA,GAAIgD,KAAK20B,eAAiBzb,EAAYlZ,KAAKy0B,SAAS,IAAMz0B,KAAKy0B,SAAS,GAAK,EAG3E,OAFAz0B,KAAK20B,cAAgB,UACrB30B,KAAK21B,SAIP,IAAK,IAAI32B,EAAIgB,KAAKy0B,SAASr8B,OAAQ4G,KACVgB,KAAK20B,gBAAkB30B,KAAK00B,SAAS11B,IACxDka,GAAalZ,KAAKy0B,SAASz1B,UACM,IAAzBgB,KAAKy0B,SAASz1B,EAAI,IAAsBka,EAAYlZ,KAAKy0B,SAASz1B,EAAI,KAGhFgB,KAAK01B,UAAU11B,KAAK00B,SAAS11B,KAKnC02B,UAAU14B,GACRgD,KAAK20B,cAAgB33B,EAErBgD,KAAK21B,SAEL,MAAMC,EAAUtB,GAAoBh9B,MAAM,KACvC6Q,IAAIlR,GAAa,GAAEA,qBAA4B+F,OAAY/F,WAAkB+F,OAE1E64B,EAAO5uB,EAAeK,QAAQsuB,EAAQxtB,KAAK,KAAMpI,KAAK+J,QAAQ/M,QAEpE64B,EAAK97B,UAAUsS,IAjLO,UAkLlBwpB,EAAK97B,UAAUC,SAnLU,iBAoL3BiN,EAAeK,QA1KY,mBA0KsBuuB,EAAKjxB,QA3KlC,cA4KjB7K,UAAUsS,IApLO,UAsLpBpF,EAAeS,QAAQmuB,EAnLG,qBAoLvBl9B,QAAQm9B,IAGP7uB,EAAeW,KAAKkuB,EAAY,+BAC7Bn9B,QAAQ28B,GAAQA,EAAKv7B,UAAUsS,IA3LlB,WA8LhBpF,EAAeW,KAAKkuB,EAzLH,aA0Ldn9B,QAAQo9B,IACP9uB,EAAeM,SAASwuB,EA5LX,aA6LVp9B,QAAQ28B,GAAQA,EAAKv7B,UAAUsS,IAjMtB,eAsMtB/L,EAAamB,QAAQzB,KAAKw0B,eA3MN,wBA2MsC,CACxD10B,cAAe9C,IAInB24B,SACE1uB,EAAeC,KAAKotB,GAAqBt0B,KAAK+J,QAAQ/M,QACnDmJ,OAAOuK,GAAQA,EAAK3W,UAAUC,SA7MX,WA8MnBrB,QAAQ+X,GAAQA,EAAK3W,UAAUwJ,OA9MZ,WAmNFY,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAOqvB,GAAU1vB,oBAAoB7E,KAAMzH,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAWX+H,EAAaQ,GAAG/F,OA7Oa,6BA6OgB,KAC3CkM,EAAeC,KAzOS,0BA0OrBvO,QAAQq9B,GAAO,IAAIzB,GAAUyB,MAUlC56B,EAAmBm5B,IC/OnB,MAAM0B,WAAYxyB,EAGDhI,kBACb,MAlCS,MAuCXuT,OACE,GAAKhP,KAAK2D,SAASlJ,YACjBuF,KAAK2D,SAASlJ,WAAWvC,WAAa2B,KAAKC,cAC3CkG,KAAK2D,SAAS5J,UAAUC,SA9BJ,UA+BpB,OAGF,IAAI6N,EACJ,MAAM7K,EAASrF,EAAuBqI,KAAK2D,UACrCuyB,EAAcl2B,KAAK2D,SAASiB,QA/BN,qBAiC5B,GAAIsxB,EAAa,CACf,MAAMC,EAAwC,OAAzBD,EAAY1lB,UAA8C,OAAzB0lB,EAAY1lB,SAhC7C,wBADH,UAkClB3I,EAAWZ,EAAeC,KAAKivB,EAAcD,GAC7CruB,EAAWA,EAASA,EAASzP,OAAS,GAGxC,MAAMg+B,EAAYvuB,EAChBvH,EAAamB,QAAQoG,EApDP,cAoD6B,CACzC/H,cAAeE,KAAK2D,WAEtB,KAMF,GAJkBrD,EAAamB,QAAQzB,KAAK2D,SAvD5B,cAuDkD,CAChE7D,cAAe+H,IAGH9F,kBAAmC,OAAdq0B,GAAsBA,EAAUr0B,iBACjE,OAGF/B,KAAK01B,UAAU11B,KAAK2D,SAAUuyB,GAE9B,MAAMG,EAAW,KACf/1B,EAAamB,QAAQoG,EAnEL,gBAmE6B,CAC3C/H,cAAeE,KAAK2D,WAEtBrD,EAAamB,QAAQzB,KAAK2D,SApEX,eAoEkC,CAC/C7D,cAAe+H,KAIf7K,EACFgD,KAAK01B,UAAU14B,EAAQA,EAAOvC,WAAY47B,GAE1CA,IAMJX,UAAU1+B,EAASmY,EAAW7T,GAC5B,MAIMg7B,IAJiBnnB,GAAqC,OAAvBA,EAAUqB,UAA4C,OAAvBrB,EAAUqB,SAE5EvJ,EAAeM,SAAS4H,EA3EN,WA0ElBlI,EAAeC,KAzEM,wBAyEmBiI,IAGZ,GACxBonB,EAAkBj7B,GAAag7B,GAAUA,EAAOv8B,UAAUC,SAnF5C,QAqFdq8B,EAAW,IAAMr2B,KAAKw2B,oBAAoBx/B,EAASs/B,EAAQh7B,GAE7Dg7B,GAAUC,GACZD,EAAOv8B,UAAUwJ,OAvFC,QAwFlBvD,KAAKiE,eAAeoyB,EAAUr/B,GAAS,IAEvCq/B,IAIJG,oBAAoBx/B,EAASs/B,EAAQh7B,GACnC,GAAIg7B,EAAQ,CACVA,EAAOv8B,UAAUwJ,OAlGG,UAoGpB,MAAMkzB,EAAgBxvB,EAAeK,QA1FJ,kCA0F4CgvB,EAAO77B,YAEhFg8B,GACFA,EAAc18B,UAAUwJ,OAvGN,UA0GgB,QAAhC+yB,EAAOp/B,aAAa,SACtBo/B,EAAOhxB,aAAa,iBAAiB,GAIzCtO,EAAQ+C,UAAUsS,IA/GI,UAgHe,QAAjCrV,EAAQE,aAAa,SACvBF,EAAQsO,aAAa,iBAAiB,GAGxC3K,EAAO3D,GAEHA,EAAQ+C,UAAUC,SArHF,SAsHlBhD,EAAQ+C,UAAUsS,IArHA,QAwHpB,IAAI8B,EAASnX,EAAQyD,WAKrB,GAJI0T,GAA8B,OAApBA,EAAOqC,WACnBrC,EAASA,EAAO1T,YAGd0T,GAAUA,EAAOpU,UAAUC,SAhIF,iBAgIsC,CACjE,MAAM08B,EAAkB1/B,EAAQ4N,QA5HZ,aA8HhB8xB,GACFzvB,EAAeC,KA1HU,mBA0HqBwvB,GAC3C/9B,QAAQg+B,GAAYA,EAAS58B,UAAUsS,IApIxB,WAuIpBrV,EAAQsO,aAAa,iBAAiB,GAGpChK,GACFA,IAMkB6I,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO+wB,GAAIpxB,oBAAoB7E,MAErC,GAAsB,iBAAXzH,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,UAYb+H,EAAaQ,GAAGrJ,SAzKc,wBAWD,4EA8JyC,SAAUyH,GAC1E,CAAC,IAAK,QAAQ9H,SAAS4I,KAAK2E,UAC9BzF,EAAMyD,iBAGJ/I,EAAWoG,OAIFi2B,GAAIpxB,oBAAoB7E,MAChCgP,UAUP5T,EAAmB66B,ICtMnB,MAkBMptB,GAAc,CAClBglB,UAAW,UACX+I,SAAU,UACV5I,MAAO,UAGH1lB,GAAU,CACdulB,WAAW,EACX+I,UAAU,EACV5I,MAAO,KAST,MAAM6I,WAAcpzB,EAClBC,YAAY1M,EAASuB,GACnB+Q,MAAMtS,GAENgJ,KAAK+J,QAAU/J,KAAKgK,WAAWzR,GAC/ByH,KAAKgxB,SAAW,KAChBhxB,KAAK82B,sBAAuB,EAC5B92B,KAAK+2B,yBAA0B,EAC/B/2B,KAAKoxB,gBAKevoB,yBACpB,OAAOA,GAGSP,qBAChB,OAAOA,GAGM7M,kBACb,MA1DS,QA+DXuT,OACoB1O,EAAamB,QAAQzB,KAAK2D,SAtD5B,iBAwDF5B,mBAId/B,KAAKg3B,gBAEDh3B,KAAK+J,QAAQ8jB,WACf7tB,KAAK2D,SAAS5J,UAAUsS,IA5DN,QAsEpBrM,KAAK2D,SAAS5J,UAAUwJ,OArEJ,QAsEpB5I,EAAOqF,KAAK2D,UACZ3D,KAAK2D,SAAS5J,UAAUsS,IAtEJ,QAuEpBrM,KAAK2D,SAAS5J,UAAUsS,IAtED,WAwEvBrM,KAAKiE,eAZY,KACfjE,KAAK2D,SAAS5J,UAAUwJ,OA7DH,WA8DrBjD,EAAamB,QAAQzB,KAAK2D,SAnEX,kBAqEf3D,KAAKi3B,sBAQuBj3B,KAAK2D,SAAU3D,KAAK+J,QAAQ8jB,YAG5D9e,OACO/O,KAAK2D,SAAS5J,UAAUC,SA7ET,UAiFFsG,EAAamB,QAAQzB,KAAK2D,SAxF5B,iBA0FF5B,mBAWd/B,KAAK2D,SAAS5J,UAAUsS,IA7FD,WA8FvBrM,KAAKiE,eARY,KACfjE,KAAK2D,SAAS5J,UAAUsS,IAzFN,QA0FlBrM,KAAK2D,SAAS5J,UAAUwJ,OAxFH,WAyFrBvD,KAAK2D,SAAS5J,UAAUwJ,OA1FN,QA2FlBjD,EAAamB,QAAQzB,KAAK2D,SAjGV,oBAqGY3D,KAAK2D,SAAU3D,KAAK+J,QAAQ8jB,aAG5DhqB,UACE7D,KAAKg3B,gBAEDh3B,KAAK2D,SAAS5J,UAAUC,SArGR,SAsGlBgG,KAAK2D,SAAS5J,UAAUwJ,OAtGN,QAyGpB+F,MAAMzF,UAKRmG,WAAWzR,GAST,OARAA,EAAS,IACJ+P,MACA1C,EAAYI,kBAAkBhG,KAAK2D,aAChB,iBAAXpL,GAAuBA,EAASA,EAAS,IAGtDF,EApIS,QAoIaE,EAAQyH,KAAK0D,YAAYmF,aAExCtQ,EAGT0+B,qBACOj3B,KAAK+J,QAAQ6sB,WAId52B,KAAK82B,sBAAwB92B,KAAK+2B,0BAItC/2B,KAAKgxB,SAAW9zB,WAAW,KACzB8C,KAAK+O,QACJ/O,KAAK+J,QAAQikB,SAGlBkJ,eAAeh4B,EAAOi4B,GACpB,OAAQj4B,EAAMsB,MACZ,IAAK,YACL,IAAK,WACHR,KAAK82B,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACHn3B,KAAK+2B,wBAA0BI,EAMnC,GAAIA,EAEF,YADAn3B,KAAKg3B,gBAIP,MAAM3pB,EAAcnO,EAAMY,cACtBE,KAAK2D,WAAa0J,GAAerN,KAAK2D,SAAS3J,SAASqT,IAI5DrN,KAAKi3B,qBAGP7F,gBACE9wB,EAAaQ,GAAGd,KAAK2D,SA/KA,qBA+K2BzE,GAASc,KAAKk3B,eAAeh4B,GAAO,IACpFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,oBA+K2BzE,GAASc,KAAKk3B,eAAeh4B,GAAO,IACnFoB,EAAaQ,GAAGd,KAAK2D,SA/KF,mBA+K2BzE,GAASc,KAAKk3B,eAAeh4B,GAAO,IAClFoB,EAAaQ,GAAGd,KAAK2D,SA/KD,oBA+K2BzE,GAASc,KAAKk3B,eAAeh4B,GAAO,IAGrF83B,gBACE9qB,aAAalM,KAAKgxB,UAClBhxB,KAAKgxB,SAAW,KAKI7sB,uBAAC5L,GACrB,OAAOyH,KAAKiF,MAAK,WACf,MAAMC,EAAO2xB,GAAMhyB,oBAAoB7E,KAAMzH,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjB2M,EAAK3M,GACd,MAAM,IAAIe,UAAW,oBAAmBf,MAG1C2M,EAAK3M,GAAQyH,kBAMrBuE,EAAqBsyB,IASrBz7B,EAAmBy7B,IC3NJ,CACb/xB,MAAAA,EACAM,OAAAA,EACAiE,SAAAA,EACA+E,SAAAA,GACAgY,SAAAA,GACAoE,MAAAA,GACA2B,UAAAA,GACAiI,QAAAA,GACAG,UAAAA,GACA0B,IAAAA,GACAY,MAAAA,GACA/F,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttr = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttr || (!hrefAttr.includes('#') && !hrefAttr.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttr.includes('#') && !hrefAttr.startsWith('#')) {\n      hrefAttr = `#${hrefAttr.split('#')[1]}`\n    }\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => {\n  if (!obj || typeof obj !== 'object') {\n    return false\n  }\n\n  if (typeof obj.jquery !== 'undefined') {\n    obj = obj[0]\n  }\n\n  return typeof obj.nodeType !== 'undefined'\n}\n\nconst getElement = obj => {\n  if (isElement(obj)) { // it's a jQuery object or a node element\n    return obj.jquery ? obj[0] : obj\n  }\n\n  if (typeof obj === 'string' && obj.length > 0) {\n    return document.querySelector(obj)\n  }\n\n  return null\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ? 'element' : toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new TypeError(\n        `${componentName.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n      )\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  return getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  // eslint-disable-next-line no-unused-expressions\n  element.offsetHeight\n}\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        DOMContentLoadedCallbacks.forEach(callback => callback())\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return list[!shouldGetNext && isCycleAllowed ? list.length - 1 : 0]\n  }\n\n  const listLength = list.length\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  getElement,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  typeCheckConfig,\n  isVisible,\n  isDisabled,\n  findShadowRoot,\n  noop,\n  getNextActiveElement,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded,\n  isRTL,\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst customEventsRegex = /^(mouseenter|mouseleave)/i\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, selector, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  let typeEvent = getTypeEvent(originalTypeEvent)\n  const isNative = nativeEvents.has(typeEvent)\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (customEventsRegex.test(originalTypeEvent)) {\n    const wrapFn = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFn) {\n      delegationFn = wrapFn(delegationFn)\n    } else {\n      handler = wrapFn(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.has(typeEvent)\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport {\n  executeAfterTransition,\n  getElement\n} from './util/index'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst VERSION = '5.1.1'\n\nclass BaseComponent {\n  constructor(element) {\n    element = getElement(element)\n\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    Object.getOwnPropertyNames(this).forEach(propertyName => {\n      this[propertyName] = null\n    })\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  /** Static */\n\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toL<PERSON>er<PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n\n    Object.keys(element.dataset)\n      .filter(key => key.startsWith('bs'))\n      .forEach(key => {\n        let pureKey = key.replace(/^bs/, '')\n        pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n        attributes[pureKey] = normalizeData(element.dataset[key])\n      })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + window.pageYOffset,\n      left: rect.left + window.pageXOffset\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children)\n      .filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (ancestor.matches(selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(', ')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  getNextActiveElement,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_INDICATOR = '[data-bs-target]'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const order = index > activeIndex ?\n      ORDER_NEXT :\n      ORDER_PREV\n\n    this._slide(order, this._items[index])\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    this._slide(direction > 0 ? DIRECTION_RIGHT : DIRECTION_LEFT)\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const hasPointerPenTouch = event => {\n      return this._pointerEvent &&\n        (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n    }\n\n    const start = event => {\n      if (hasPointerPenTouch(event)) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.touches && event.touches.length > 1 ?\n        0 :\n        event.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (hasPointerPenTouch(event)) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(direction)\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByOrder(order, activeElement) {\n    const isNext = order === ORDER_NEXT\n    return getNextActiveElement(this._items, activeElement, isNext, this._config.wrap)\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n      activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n      activeIndicator.removeAttribute('aria-current')\n\n      const indicators = SelectorEngine.find(SELECTOR_INDICATOR, this._indicatorsElement)\n\n      for (let i = 0; i < indicators.length; i++) {\n        if (Number.parseInt(indicators[i].getAttribute('data-bs-slide-to'), 10) === this._getItemIndex(element)) {\n          indicators[i].classList.add(CLASS_NAME_ACTIVE)\n          indicators[i].setAttribute('aria-current', 'true')\n          break\n        }\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(directionOrOrder, element) {\n    const order = this._directionToOrder(directionOrOrder)\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || this._getItemByOrder(order, activeElement)\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    const isNext = order === ORDER_NEXT\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n    const eventDirectionName = this._orderToDirection(order)\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    if (this._isSliding) {\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const triggerSlidEvent = () => {\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const completeCallBack = () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(triggerSlidEvent, 0)\n      }\n\n      this._queueCallback(completeCallBack, activeElement, true)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      triggerSlidEvent()\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (![DIRECTION_RIGHT, DIRECTION_LEFT].includes(direction)) {\n      return direction\n    }\n\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (![ORDER_NEXT, ORDER_PREV].includes(order)) {\n      return order\n    }\n\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    const data = Carousel.getOrCreateInstance(element, config)\n\n    let { _config } = data\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-bs-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Carousel.getInstance(target).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Carousel.getInstance(carousels[i]))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  getElementFromSelector,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._isTransitioning = false\n    this._config = this._getConfig(config)\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let actives = []\n    let activesData\n\n    if (this._config.parent) {\n      const children = SelectorEngine.find(`.${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`, this._config.parent)\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._config.parent).filter(elem => !children.includes(elem)) // remove children if greater depth\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives.length) {\n      const tempActiveData = actives.find(elem => container !== elem)\n      activesData = tempActiveData ? Collapse.getInstance(tempActiveData) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    actives.forEach(elemActive => {\n      if (container !== elemActive) {\n        Collapse.getOrCreateInstance(elemActive, { toggle: false }).hide()\n      }\n\n      if (!activesData) {\n        Data.set(elemActive, DATA_KEY, null)\n      }\n    })\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    for (let i = 0; i < triggerArrayLength; i++) {\n      const trigger = this._triggerArray[i]\n      const elem = getElementFromSelector(trigger)\n\n      if (elem && !this._isShown(elem)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = SelectorEngine.find(`.${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`, this._config.parent)\n    SelectorEngine.find(SELECTOR_DATA_TOGGLE, this._config.parent).filter(elem => !children.includes(elem))\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        if (selected) {\n          this._addAriaAndCollapsedClass([element], this._isShown(selected))\n        }\n      })\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const _config = {}\n      if (typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "import { isHTMLElement } from \"./instanceOf.js\";\nvar round = Math.round;\nexport default function getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (isHTMLElement(element) && includeScale) {\n    var offsetHeight = element.offsetHeight;\n    var offsetWidth = element.offsetWidth; // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n    // Fallback to 1 in case both values are `0`\n\n    if (offsetWidth > 0) {\n      scaleX = rect.width / offsetWidth || 1;\n    }\n\n    if (offsetHeight > 0) {\n      scaleY = rect.height / offsetHeight || 1;\n    }\n  }\n\n  return {\n    width: round(rect.width / scaleX),\n    height: round(rect.height / scaleY),\n    top: round(rect.top / scaleY),\n    right: round(rect.right / scaleX),\n    bottom: round(rect.bottom / scaleY),\n    left: round(rect.left / scaleX),\n    x: round(rect.left / scaleX),\n    y: round(rect.top / scaleY)\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport default function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport within from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(round(x * dpr) / dpr) || 0,\n    y: round(round(y * dpr) / dpr) || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets;\n\n  var _ref3 = roundOffsets === true ? roundOffsetsByDPR(offsets) : typeof roundOffsets === 'function' ? roundOffsets(offsets) : offsets,\n      _ref3$x = _ref3.x,\n      x = _ref3$x === void 0 ? 0 : _ref3$x,\n      _ref3$y = _ref3.y,\n      y = _ref3$y === void 0 ? 0 : _ref3$y;\n\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom; // $FlowFixMe[prop-missing]\n\n      y -= offsetParent[heightProp] - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right; // $FlowFixMe[prop-missing]\n\n      x -= offsetParent[widthProp] - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref4) {\n  var state = _ref4.state,\n      options = _ref4.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isHTMLElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\";\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport within from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { max as mathMax, min as mathMin } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis || checkAltAxis) {\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = popperOffsets[mainAxis] + overflow[mainSide];\n    var max = popperOffsets[mainAxis] - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - tetherOffsetValue : minLen - arrowLen - arrowPaddingMin - tetherOffsetValue;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + tetherOffsetValue : maxLen + arrowLen + arrowPaddingMax + tetherOffsetValue;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = state.modifiersData.offset ? state.modifiersData.offset[state.placement][mainAxis] : 0;\n    var tetherMin = popperOffsets[mainAxis] + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = popperOffsets[mainAxis] + maxOffset - offsetModifierValue;\n\n    if (checkMainAxis) {\n      var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n      popperOffsets[mainAxis] = preventedOffset;\n      data[mainAxis] = preventedOffset - offset;\n    }\n\n    if (checkAltAxis) {\n      var _mainSide = mainAxis === 'x' ? top : left;\n\n      var _altSide = mainAxis === 'x' ? bottom : right;\n\n      var _offset = popperOffsets[altAxis];\n\n      var _min = _offset + overflow[_mainSide];\n\n      var _max = _offset - overflow[_altSide];\n\n      var _preventedOffset = within(tether ? mathMin(_min, tetherMin) : _min, _offset, tether ? mathMax(_max, tetherMax) : _max);\n\n      popperOffsets[altAxis] = _preventedOffset;\n      data[altAxis] = _preventedOffset - _offset;\n    }\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = rect.width / element.offsetWidth || 1;\n  var scaleY = rect.height / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_NAVBAR = 'navbar'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    // Totally disable Popper for Dropdowns in Navbar\n    if (this._inNavbar) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'none')\n    } else {\n      this._createPopper(parent)\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', noop))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown(this._menu)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.off(elem, 'mouseover', noop))\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper(parent) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    const isDisplayStatic = popperConfig.modifiers.find(modifier => modifier.name === 'applyStyles' && modifier.enabled === false)\n\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n\n    if (isDisplayStatic) {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static')\n    }\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(`.${CLASS_NAME_NAVBAR}`) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const context = Dropdown.getInstance(toggles[i])\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      if (!context._isShown()) {\n        continue\n      }\n\n      const relatedTarget = {\n        relatedTarget: context._element\n      }\n\n      if (event) {\n        const composedPath = event.composedPath()\n        const isMenuTarget = composedPath.includes(context._menu)\n        if (\n          composedPath.includes(context._element) ||\n          (context._config.autoClose === 'inside' && !isMenuTarget) ||\n          (context._config.autoClose === 'outside' && isMenuTarget)\n        ) {\n          continue\n        }\n\n        // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n        if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n          continue\n        }\n\n        if (event.type === 'click') {\n          relatedTarget.clickEvent = event\n        }\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (!isActive && event.key === ESCAPE_KEY) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (event.key === ESCAPE_KEY) {\n      instance.hide()\n      return\n    }\n\n    if (event.key === ARROW_UP_KEY || event.key === ARROW_DOWN_KEY) {\n      if (!isActive) {\n        instance.show()\n      }\n\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, 'paddingRight', calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight', calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight', calculatedValue => calculatedValue - width)\n  }\n\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProp, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProp)\n      const calculatedValue = window.getComputedStyle(element)[styleProp]\n      element.style[styleProp] = `${callback(Number.parseFloat(calculatedValue))}px`\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, 'paddingRight')\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, 'marginRight')\n  }\n\n  _saveInitialAttribute(element, styleProp) {\n    const actualValue = element.style[styleProp]\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProp, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProp) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProp)\n      if (typeof value === 'undefined') {\n        element.style.removeProperty(styleProp)\n      } else {\n        Manipulator.removeDataAttribute(element, styleProp)\n        element.style[styleProp] = value\n      }\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n    } else {\n      SelectorEngine.find(selector, this._element).forEach(callBack)\n    }\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow, typeCheckConfig } from './index'\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nclass Backdrop {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    if (this._config.isAnimated) {\n      reflow(this._getElement())\n    }\n\n    this._getElement().classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  // Private\n\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    this._config.rootElement.append(this._getElement())\n\n    EventHandler.on(this._getElement(), EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { typeCheckConfig } from './index'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nclass FocusTrap {\n  constructor(config) {\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  activate() {\n    const { trapElement, autofocus } = this._config\n\n    if (this._isActive) {\n      return\n    }\n\n    if (autofocus) {\n      trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n\n  _handleFocusin(event) {\n    const { target } = event\n    const { trapElement } = this._config\n\n    if (\n      target === document ||\n      target === trapElement ||\n      trapElement.contains(target)\n    ) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isRTL,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    if (this._isAnimated()) {\n      this._isTransitioning = true\n    }\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const isAnimated = this._isAnimated()\n\n    if (isAnimated) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    this._queueCallback(() => this._hideModal(), this._element, isAnimated)\n  }\n\n  dispose() {\n    [window, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const isAnimated = this._isAnimated()\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode || this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (isAnimated) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, isAnimated)\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _showBackdrop(callback) {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (this._ignoreBackdropClick) {\n        this._ignoreBackdropClick = false\n        return\n      }\n\n      if (event.target !== event.currentTarget) {\n        return\n      }\n\n      if (this._config.backdrop === true) {\n        this.hide()\n      } else if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n      }\n    })\n\n    this._backdrop.show(callback)\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const { classList, scrollHeight, style } = this._element\n    const isModalOverflowing = scrollHeight > document.documentElement.clientHeight\n\n    // return if the following background transition hasn't yet completed\n    if ((!isModalOverflowing && style.overflowY === 'hidden') || classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      style.overflowY = 'hidden'\n    }\n\n    classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        this._queueCallback(() => {\n          style.overflowY = ''\n        }, this._dialog)\n      }\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if ((!isBodyOverflowing && isModalOverflowing && !isRTL()) || (isBodyOverflowing && !isModalOverflowing && isRTL())) {\n      this._element.style.paddingLeft = `${scrollbarWidth}px`\n    }\n\n    if ((isBodyOverflowing && !isModalOverflowing && !isRTL()) || (!isBodyOverflowing && isModalOverflowing && isRTL())) {\n      this._element.style.paddingRight = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking moddal toggler while another one is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen) {\n    Modal.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible,\n  typeCheckConfig\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Manipulator from './dom/manipulator'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: 'boolean',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._element.style.visibility = 'visible'\n\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.setAttribute('aria-hidden', true)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n      this._element.style.visibility = 'hidden'\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' ? config : {})\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _initializeBackDrop() {\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible: this._config.backdrop,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: () => this.hide()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (this._config.keyboard && event.key === ESCAPE_KEY) {\n        this.hide()\n      }\n    })\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const allReadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (allReadyOpen && allReadyOpen !== target) {\n    Offcanvas.getInstance(allReadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () =>\n  SelectorEngine.find(OPEN_SELECTOR).forEach(el => Offcanvas.getOrCreateInstance(el).show())\n)\n\nenableDismissTrigger(Offcanvas)\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attrName)) {\n    if (uriAttrs.has(attrName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (!allowlistKeys.includes(elName)) {\n      el.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\n\nimport {\n  defineJQueryPlugin,\n  findShadowRoot,\n  getElement,\n  getUID,\n  isElement,\n  isRTL,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport { DefaultAllowlist, sanitizeHtml } from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element)\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this._config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this.isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.Event.SHOW)\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = shadowRoot === null ?\n      this._element.ownerDocument.documentElement.contains(this._element) :\n      shadowRoot.contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // A trick to recreate a tooltip in case a new title is given by using the NOT documented `data-bs-original-title`\n    // This will be removed later in favor of a `setContent` method\n    if (this.constructor.NAME === 'tooltip' && this.tip && this.getTitle() !== this.tip.querySelector(SELECTOR_TOOLTIP_INNER).innerHTML) {\n      this._disposePopper()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    const tip = this.getTipElement()\n    const tipId = getUID(this.constructor.NAME)\n\n    tip.setAttribute('id', tipId)\n    this._element.setAttribute('aria-describedby', tipId)\n\n    if (this._config.animation) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n\n    const attachment = this._getAttachment(placement)\n    this._addAttachmentClass(attachment)\n\n    const { container } = this._config\n    Data.set(tip, this.constructor.DATA_KEY, this)\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.Event.INSERTED)\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    const customClass = this._resolvePossibleFunction(this._config.customClass)\n    if (customClass) {\n      tip.classList.add(...customClass.split(' '))\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children).forEach(element => {\n        EventHandler.on(element, 'mouseover', noop)\n      })\n    }\n\n    const complete = () => {\n      const prevHoverState = this._hoverState\n\n      this._hoverState = null\n      EventHandler.trigger(this._element, this.constructor.Event.SHOWN)\n\n      if (prevHoverState === HOVER_STATE_OUT) {\n        this._leave(null, this)\n      }\n    }\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (this._hoverState !== HOVER_STATE_SHOW) {\n        tip.remove()\n      }\n\n      this._cleanTipClass()\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.Event.HIDDEN)\n\n      this._disposePopper()\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    const isAnimated = this.tip.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(complete, this.tip, isAnimated)\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this._config.template\n\n    const tip = element.children[0]\n    this.setContent(tip)\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n\n    this.tip = tip\n    return this.tip\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TOOLTIP_INNER)\n  }\n\n  _sanitizeAndSetContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!content && templateElement) {\n      templateElement.remove()\n      return\n    }\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(templateElement, content)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (isElement(content)) {\n      content = getElement(content)\n\n      // content is a DOM node or a jQuery\n      if (this._config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.append(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this._config.html) {\n      if (this._config.sanitize) {\n        content = sanitizeHtml(content, this._config.allowList, this._config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    const title = this._element.getAttribute('data-bs-original-title') || this._config.title\n\n    return this._resolvePossibleFunction(title)\n  }\n\n  updateAttachment(attachment) {\n    if (attachment === 'right') {\n      return 'end'\n    }\n\n    if (attachment === 'left') {\n      return 'start'\n    }\n\n    return attachment\n  }\n\n  // Private\n\n  _initializeOnDelegatedTarget(event, context) {\n    return context || this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(val => Number.parseInt(val, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(content) {\n    return typeof content === 'function' ? content.call(this._element) : content\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'onChange',\n          enabled: true,\n          phase: 'afterWrite',\n          fn: data => this._handlePopperPlacementChange(data)\n        }\n      ],\n      onFirstUpdate: data => {\n        if (data.options.placement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      }\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${this._getBasicClassPrefix()}-${this.updateAttachment(attachment)}`)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.Event.CLICK, this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => this._enter(event))\n        EventHandler.on(this._element, eventOut, this._config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n    const originalTitleType = typeof this._element.getAttribute('data-bs-original-title')\n\n    if (title || originalTitleType !== 'string') {\n      this._element.setAttribute('data-bs-original-title', title || '')\n      if (title && !this._element.getAttribute('aria-label') && !this._element.textContent) {\n        this._element.setAttribute('aria-label', title)\n      }\n\n      this._element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context._config.delay || !context._config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context._config.delay.show)\n  }\n\n  _leave(event, context) {\n    context = this._initializeOnDelegatedTarget(event, context)\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = context._element.contains(event.relatedTarget)\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context._config.delay || !context._config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context._config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttr)) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const basicClassPrefixRegex = new RegExp(`(^|\\\\s)${this._getBasicClassPrefix()}\\\\S+`, 'g')\n    const tabClass = tip.getAttribute('class').match(basicClassPrefixRegex)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    const { state } = popperData\n\n    if (!state) {\n      return\n    }\n\n    this.tip = state.elements.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(state.placement))\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent(tip) {\n    this._sanitizeAndSetContent(tip, this.getTitle(), SELECTOR_TITLE)\n    this._sanitizeAndSetContent(tip, this._getContent(), SELECTOR_CONTENT)\n  }\n\n  // Private\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  _getBasicClassPrefix() {\n    return CLASS_PREFIX\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getSelectorFromElement,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}, .${CLASS_NAME_DROPDOWN_ITEM}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n    this._scrollElement = this._element.tagName === 'BODY' ? window : this._element\n    this._config = this._getConfig(config)\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, () => this._process())\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    config.target = getElement(config.target) || document.documentElement\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' || scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = SELECTOR_LINK_ITEMS.split(',')\n      .map(selector => `${selector}[data-bs-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','), this._config.target)\n\n    link.classList.add(CLASS_NAME_ACTIVE)\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(SELECTOR_LINK_ITEMS, this._config.target)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab extends BaseComponent {\n  // Getters\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE))) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = previous ?\n      EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      }) :\n      null\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented || (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(this._element, listElement)\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(element, active, callback)\n\n    if (active && isTransitioning) {\n      active.classList.remove(CLASS_NAME_SHOW)\n      this._queueCallback(complete, element, true)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && parent.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE, dropdownElement)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  const data = Tab.getOrCreateInstance(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element)\n\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\nenableDismissTrigger(Toast)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.1.1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}