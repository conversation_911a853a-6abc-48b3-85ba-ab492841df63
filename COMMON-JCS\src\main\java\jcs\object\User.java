package jcs.object;

import java.util.List;

import jcs.basebean.JcsBaseBean;

/**
 * 使用者物件。
 */
public class User extends JcsBaseBean{
	private static final long serialVersionUID = 1L;

	/** 帳號 **/
	String id;
	
	/** 姓名 **/
	String name;
	
	/** 統編 **/
	String cid;
	
	/** 單位 **/
	Dept dept;
	
	/** 登入主機名稱 **/
	String server;
	
	/** 登入環境 **/
	String env;
	
	/** 登入位址 **/
	String ip;
	
	/** 登入時間 **/
	String logintime;
	
	/** 登入碼 **/
	String session;
	
	/** 登入瀏灠器 **/
	String agent;
	
	/** 語系 **/
	String locale;
	
	/** 日期格式 **/
	String format;
	
	/** 所有權限 **/
	List<Role> role;
	
	/** 目前執行權限 **/
	Role execrole;
	
	/** 安控管理員 **/
	String regid;

	/** 安控管理員姓名 **/
	String regname;
	
	/** 註冊日期 **/
	String regtime;
	
	/** 授權管理員 **/
	String authid;
	
	/** 授權管理員姓名 **/
	String authname;
	
	/** 授權日期 **/
	String authtime;
	
	public User() {
		
	}
	
	public String getId(){
		return id;
	}
	public void setId(String id){
		this.id = id;
	}
	
	public String getName(){
		return name;
	}
	public void setName(String name){
		this.name = name;
	}
	
	public String getCid(){
		return cid;
	}
	public void setCid(String cid){
		this.cid = cid;
	}
	
	public Dept getDept() {
		return dept;
	}
	public void setDept(Dept dept) {
		this.dept = dept;
	}
	
	public String getServer(){
		return server;
	}
	public void setServer(String server){
		this.server = server;
	}
	
	public String getEnv(){
		return env;
	}
	public void setEnv(String env){
		this.env = env;
	}
	
	public String getIp(){
		return ip;
	}
	public void setIp(String ip){
		this.ip = ip;
	}
	
	public String getLogintime(){
		return logintime;
	}
	public void setLogintime(String logintime){
		this.logintime = logintime;
	}
	
	public String getSession(){
		return session;
	}
	public void setSession(String session){
		this.session = session;
	}
	
	public String getAgent(){
		return agent;
	}
	public void setAgent(String agent){
		this.agent = agent;
	}
	
	public String getLocale(){
		return locale;
	}
	public void setLocale(String locale){
		this.locale = locale;
	}
	
	public String getFormat(){
		return format;
	}
	public void setFormat(String format){
		this.format = format;
	}
	
	public List<Role> getRole(){
		return role;
	}
	public void setRole(List<Role> list){
		this.role = list;
	}
	
	public Role getExecrole(){
		return execrole;
	}
	public void setExecrole(Role execrole){
		this.execrole = execrole;
	}
	
	public String getRegid() {
		return regid;
	}
	public void setRegid(String regid) {
		this.regid = regid;
	}

	public String getRegname() {
		return regname;
	}
	public void setRegname(String regname) {
		this.regname = regname;
	}

	public String getRegtime() {
		return regtime;
	}
	public void setRegtime(String regtime) {
		this.regtime = regtime;
	}

	public String getAuthid() {
		return authid;
	}
	public void setAuthid(String authid) {
		this.authid = authid;
	}

	public String getAuthname() {
		return authname;
	}
	public void setAuthname(String authname) {
		this.authname = authname;
	}

	public String getAuthtime() {
		return authtime;
	}
	public void setAuthtime(String authtime) {
		this.authtime = authtime;
	}
	
}
