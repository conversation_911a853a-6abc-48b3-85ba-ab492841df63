package com.ejcic.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;

import lombok.extern.slf4j.Slf4j;

/**
 * 配置 Spring Security 的安全設置。
 */
@Slf4j
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Value("${security.protected.urls}")
    private String[] protectedUrls;

    /**
     * 配置安全過濾鏈，定義 HTTP 請求的授權規則。
     *
     * @param http HttpSecurity
     * @return SecurityFilterChain
     * @throws Exception
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("Configuring security filter chain............................");
        log.info("Protected URLs: {}", (Object) protectedUrls);
        http.authorizeHttpRequests(authz -> authz
                .requestMatchers(protectedUrls).authenticated() // 需要認證
                .anyRequest().permitAll() // 其他請求不需要認證
        )
                .formLogin(form -> form
                        .loginPage("/logon") // 設置自定義登錄頁面
                        .permitAll() // 允許所有人訪問登錄頁面
                );
        return http.build();
    }

}
