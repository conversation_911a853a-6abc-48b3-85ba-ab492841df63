package jcs.autoconfigure;

import java.util.List;

import org.sitemesh.config.ConfigurableSiteMeshFilter;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.springframework.web.servlet.view.freemarker.FreeMarkerViewResolver;

import lombok.extern.slf4j.Slf4j;

/**
 * CommonWebAutoConfiguration
 *
 * 提供共用的 Web 組態（Filter / Interceptor / ViewResolver / Freemarker / SiteMesh）。
 * - 僅在 Web 應用環境下注入（@ConditionalOnWebApplication）
 * - 全部以 @ConditionalOnMissingBean 設計，確保應用端可覆寫
 */
@AutoConfiguration
@ConditionalOnWebApplication
@ConditionalOnClass(WebMvcConfigurer.class)
@Slf4j
public class CommonWebAutoConfiguration {

    /**
     * SiteMesh Filter 註冊。
     * - 僅在 classpath 存在 SiteMesh 時生效
     * - 可被名稱為 "siteMeshFilter" 的 Bean 覆寫
     *
     * @return FilterRegistrationBean 已註冊 SiteMesh Filter
     */
    @Bean
    @ConditionalOnClass(ConfigurableSiteMeshFilter.class)
    @ConditionalOnMissingBean(name = "siteMeshFilter")
    public FilterRegistrationBean<ConfigurableSiteMeshFilter> siteMeshFilter() {
        log.info("Auto-configuring SiteMesh filter");
        FilterRegistrationBean<ConfigurableSiteMeshFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new ConfigurableSiteMeshFilter());
        bean.addUrlPatterns("/*");
        bean.setName("siteMeshFilter");
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        return bean;
    }

    /**
     * FreeMarker 模板配置。
     * - 可被應用端同型別 Bean 覆寫
     *
     * @return FreeMarkerConfigurer
     */
    @Bean
    @ConditionalOnClass(FreeMarkerConfigurer.class)
    @ConditionalOnMissingBean(FreeMarkerConfigurer.class)
    public FreeMarkerConfigurer freeMarkerConfigurer() {
        FreeMarkerConfigurer configurer = new FreeMarkerConfigurer();
        configurer.setTemplateLoaderPath("/form/");
        configurer.setDefaultEncoding("UTF-8");
        return configurer;
    }

    /**
     * FreeMarker 視圖解析器。
     * - contentType 與順序對齊既有專案設定
     *
     * @return ViewResolver
     */
    @Bean
    @ConditionalOnClass(FreeMarkerViewResolver.class)
    @ConditionalOnMissingBean(name = "freeMarkerViewResolver")
    public ViewResolver freeMarkerViewResolver() {
        FreeMarkerViewResolver resolver = new FreeMarkerViewResolver();
        resolver.setContentType("text/html; charset=utf-8");
        resolver.setCache(true);
        resolver.setSuffix(".ftl");
        resolver.setOrder(1);
        return resolver;
    }

    /**
     * JSP 視圖解析器（InternalResourceViewResolver）。
     *
     * @return ViewResolver
     */
    @Bean
    @ConditionalOnClass(InternalResourceViewResolver.class)
    @ConditionalOnMissingBean(name = "jspViewResolver")
    public ViewResolver jspViewResolver() {
        InternalResourceViewResolver resolver = new InternalResourceViewResolver();
        resolver.setCache(true);
        resolver.setPrefix("/pages/");
        resolver.setSuffix(".jsp");
        resolver.setOrder(2);
        return resolver;
    }

    /**
     * 預設的 WebMvcConfigurer：
     * - 若應用端沒有自定義 WebMvcConfigurer，則由此註冊所有現有的 HandlerInterceptor Bean
     * - 為每個攔截器設定預設路徑規則（攔截 /**，排除 /actuator/** 與 /assets/**）
     *
     * @param interceptorsProvider 提供目前容器中的 HandlerInterceptor Bean 清單
     * @return WebMvcConfigurer
     */
    @Bean
    @ConditionalOnMissingBean(WebMvcConfigurer.class)
    public WebMvcConfigurer defaultWebMvcConfigurer(ObjectProvider<List<HandlerInterceptor>> interceptorsProvider) {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(@NonNull InterceptorRegistry registry) {
                List<HandlerInterceptor> interceptors = interceptorsProvider.getIfAvailable(java.util.List::of);
                if (interceptors != null && !interceptors.isEmpty()) {
                    log.info("Auto-registering {} HandlerInterceptors", interceptors.size());
                    for (HandlerInterceptor interceptor : interceptors) {
                        registry.addInterceptor(interceptor)
                                .addPathPatterns("/**")
                                .excludePathPatterns("/actuator/**", "/assets/**");
                    }
                }
            }
        };
    }
}
