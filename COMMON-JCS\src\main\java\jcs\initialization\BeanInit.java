package jcs.initialization;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.Converter;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jcs.constant.CommonConstant;
import jcs.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 這是一個公用程式，提供bean的處理函數。
 */
@Slf4j
@Component
public class BeanInit {

	/**
	 * 初始化。
	 */
	@PostConstruct
	public void init() {
		log.info("=====================================================================");
		register();
		log.info("=====================================================================");
	}

	/**
	 * 注冊轉換器 通過查看API文檔，可知Converter是一個接口，需要實現其方法,第二個參數為想要轉換成的數據類型。
	 */
	@SuppressWarnings("unchecked")
	public static void register() {
		log.info("BeanUtil註冊文字轉換器");
		ConvertUtils.register(new Converter() {
			public <T> T convert(Class<T> type, Object value) {
				if (StringUtil.isNotEmpty(value)) {
					if (CommonConstant.NULL_TO_SPACE.equals(value.toString()))
						return (T) CommonConstant.EMPTY;
					return (T) StringUtil.trim(value);
				}
				return null;
			}
		}, String.class);
	}
}
