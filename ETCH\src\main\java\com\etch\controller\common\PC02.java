package com.etch.controller.common;

import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping("/home")
public class PC02 {

    @RequestMapping
    public String execute(@RequestParam Map<String, Object> params, Model model) {
        log.info("用戶訪問首頁，參數: {}", params);
        return "common/PC02";
    }

}
