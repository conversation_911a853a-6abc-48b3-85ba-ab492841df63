package jcs.tool.generator;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Scanner;

import org.apache.commons.lang3.StringUtils;

import jcs.tool.exec.Tool;
import jcs.tool.bean.TableBean;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成Dao物件。
 */
public class DaoGenerator extends Tool{
	// 套件路徑
	private static final String classpath = "com.dao";
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableupdate, args);
		for(WorkBean bean : workList){
			Integer count = 0;
			System.out.println("\n");
			System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
			System.out.println("🈠 開始執行 " + bean.getName() + "『DaoGenerator』轉換");
			String from = getWorkRoot() + "DOC\\" + bean.getName() + "\\1、資料庫設計\\1.3、表格內容說明\\"; // Doc檔案來源
			String to = getWorkRoot() + bean.getProject() + "-JPA\\src\\main\\java\\com\\dao\\"; // Dao產出至...
			File folder = new File(from);
			File[] files = folder.listFiles();
			if(files != null){
				for(File file : files){
					if(file.isDirectory()){
						File[] files2 = file.listFiles();
						if(files2 != null){
							for(File file2 : files2){
								if(Tool.isCreate(file2)){
									TableBean tablebean = new TableBean().parse(
											from + file.getName() + "\\" + file2.getName(), to, TableBean.Dao, file.getName());
									if(StringUtils.endsWithAny(tablebean.getEname(), new String[]{"0OLD"})){
										continue;
									}
									// 產檔-------------------------------------------------------
									new DaoGenerator().create(tablebean);
									if("True".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname("H" + ename);
										tablebean.setCname(cname + "(歷史)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname("V" + ename);
										tablebean.setCname(cname + "(視圖)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									} else if("Approved".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname(ename + "M");
										tablebean.setCname(cname + "(核定檔)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									}
									// 專案參照-----------------------------------------------------
									for(String references : tablebean.getReferenceslist()){
										File referencesPath = new File(getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\dao\\");
										if(referencesPath.exists()){
											tablebean.setTo(getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\dao\\");
											new DaoGenerator().create(tablebean);
											if("True".equals(tablebean.getHistory())){
												String ename = tablebean.getEname();
												String cname = tablebean.getCname();
												tablebean.setEname("H" + ename);
												tablebean.setCname(cname + "(歷史)");
												new DaoGenerator().create(tablebean);
												tablebean.setEname("V" + ename);
												tablebean.setCname(cname + "(視圖)");
												new DaoGenerator().create(tablebean);
												tablebean.setEname(ename);
												tablebean.setCname(cname);
											} else if("Approved".equals(tablebean.getHistory())){
												String ename = tablebean.getEname();
												String cname = tablebean.getCname();
												tablebean.setEname(ename + "M");
												tablebean.setCname(cname + "(核定檔)");
												new DaoGenerator().create(tablebean);
												tablebean.setEname(ename);
												tablebean.setCname(cname);
											}
											tablebean.setTo(to);
										}else{
											System.out.println("● 產製失敗 ●");
											System.out.println("系統找不到指定的路徑：" + referencesPath);
										}
									}
									count++;
								}
							}
						}
					}else{
						if(Tool.isCreate(file)){
							TableBean tablebean = new TableBean().parse(
									from + file.getName(), to, TableBean.Dao, file.getName());
							// 產檔-------------------------------------------------------
							new DaoGenerator().create(tablebean);
							if("True".equals(tablebean.getHistory())){
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname("H" + ename);
								tablebean.setCname(cname + "(歷史)");
								new DaoGenerator().create(tablebean);
								tablebean.setEname("V" + ename);
								tablebean.setCname(cname + "(視圖)");
								new DaoGenerator().create(tablebean);
								tablebean.setEname(ename);
								tablebean.setCname(cname);
							} else if("Approved".equals(tablebean.getHistory())){
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname(ename + "M");
								tablebean.setCname(cname + "(核定檔)");
								new DaoGenerator().create(tablebean);
								tablebean.setEname(ename);
								tablebean.setCname(cname);
							}
							// 專案參照-----------------------------------------------------
							for(String references : tablebean.getReferenceslist()){
								File referencesPath = new File(getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\dao\\");
								if(referencesPath.exists()){
									tablebean.setTo(getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\dao\\");
									new DaoGenerator().create(tablebean);
									if("True".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname("H" + ename);
										tablebean.setCname(cname + "(歷史)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname("V" + ename);
										tablebean.setCname(cname + "(視圖)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									} else if("Approved".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname(ename + "M");
										tablebean.setCname(cname + "(核定檔)");
										new DaoGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									}
									tablebean.setTo(to);
								}else{
									System.out.println("● 產製失敗 ●");
									System.out.println("系統找不到指定的路徑：" + referencesPath);
								}
							}
							count++;
						}
					}
				}
			}
			System.out.println("🈡 執行結果 " + bean.getName() + "共轉換 " + count + "個");
			System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
		}
		sc.close();
	}
	
	/**
	 * 產出Dao物件。
	 */
	public void create(TableBean tablebean){
		FileWriter fw = null;
		if(tablebean.getKeyColumlist().size() > 0){
			File daofile = new File(tablebean.getTo() + tablebean.getOrmName() + "Dao.java");
			if(daofile.exists()){
				System.out.println("● Dao已存在不產製 ●");
			}else{
				try{
					fw = new FileWriter(tablebean.getTo() + tablebean.getOrmName() + "Dao.java");
					StringBuilder sb = new StringBuilder();
					sb.append("package " + classpath + ";\n\n");
					sb.append("import org.springframework.stereotype.Repository;").append("\n");
					sb.append("\n");
					sb.append("import com.bean.").append(tablebean.getOrmName()).append(";").append("\n");
					sb.append("\n");
					sb.append("import jcs.beans.CustomJpaDao;").append("\n");
					sb.append("\n");
					sb.append("/**").append("\n");
					sb.append(" * Spring Data JPA Java Persistence Query Language(JPQL)").append("\n");
					sb.append(" */").append("\n");
					sb.append("@Repository").append("\n");
					sb.append("public interface ").append(tablebean.getOrmName()).append("Dao extends CustomJpaDao<");
					sb.append(tablebean.getOrmName()).append(", ").append(tablebean.getOrmName()).append(".Key> {").append("\n");
					sb.append("	// 名稱定義查詢-------------------------------------------------------------------").append("\n");
					sb.append("	// NativeQuery-----------------------------------------------------------------").append("\n");
					sb.append("	// JPQL------------------------------------------------------------------------").append("\n");
					sb.append("}").append("\n");
					fw.write(sb.toString());
					fw.flush();
					System.out.println("● 產製成功 ●");
				}catch(Exception e){
					e.printStackTrace();
				}finally{
					try{
						if(fw != null)
							fw.close();
					}catch(IOException e){
						e.printStackTrace();
					}
				}
			}
		}else{
			System.out.println("●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●");
			System.out.println("無Key值不產製");
			System.out.println("●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●");
		}
	}
}
