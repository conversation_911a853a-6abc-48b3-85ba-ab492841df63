<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- Parent: reuse Spring Boot's sensible defaults and BOM management -->
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.5.5</version>
    <relativePath />
  </parent>

  <groupId>tcb.jcic</groupId>
  <artifactId>tcb-ejcic-parent</artifactId>
  <version>${revision}</version>
  <packaging>pom</packaging>
  <name>tcb-ejcic-parent</name>
  <description>Parent aggregator POM for TCB EJCIC multi-module project</description>

  <properties>
    <revision>1.0.0-SNAPSHOT</revision>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>21</java.version>
    <lombok.version>1.18.36</lombok.version>
    <!-- Reproducible builds timestamp (any constant ISO-8601 value) -->
    <project.build.outputTimestamp>2025-01-01T00:00:00Z</project.build.outputTimestamp>
  </properties>

  <!-- Aggregate all modules for one-shot build -->
  <modules>
    <module>COMMON-JCS</module>
    <module>COMMON-CORE</module>
    <module>COMMON-WEB-STARTER</module>
    <module>COMMON-DATA-STARTER</module>
    <module>COMMON-BANK</module>
    <module>EJCIC-JPA</module>
    <module>EJMN-JPA</module>
    <module>ETCH-JPA</module>
    <module>EJCIC</module>
    <module>EJMN</module>
    <module>ETCH</module>
    <module>TOOL</module>
  </modules>

  <!-- Centralize 3rd-party dependency versions -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>6.1.0</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>5.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi-scratchpad</artifactId>
        <version>5.4.1</version>
      </dependency>
      <dependency>
        <groupId>com.deepoove</groupId>
        <artifactId>poi-tl</artifactId>
        <version>1.12.2</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>1.21.1</version>
      </dependency>
      <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>1.11.0</version>
      </dependency>
      <dependency>
        <groupId>commons-lang</groupId>
        <artifactId>commons-lang</artifactId>
        <version>2.6</version>
      </dependency>
      <dependency>
        <groupId>org.sitemesh</groupId>
        <artifactId>sitemesh</artifactId>
        <version>3.2.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <!-- Unify plugins configuration (versions are managed by Boot parent) -->
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <release>${java.version}</release>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <argLine>--add-opens java.base/java.lang=ALL-UNNAMED</argLine>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <!-- Fill in project metadata as needed (url/licenses/developers/scm) in future -->
</project>