package jcs.object;

import jcs.basebean.JcsBaseBean;

/**
 * 單位物件。
 */
public class Dept extends JcsBaseBean {
	private static final long serialVersionUID = 1L;

	/** 單位 **/
	String branch;
	
	/** 單位名 **/
	String branchnm;
	
	/** 上層單位 **/
	String center;
	
	/** 上層單位名 **/
	String centernm;
	
	/** 種類 **/
	String type;
	
	/** 國別 **/
	String natcode;

	/** 時區 **/
	String timezone;

	/** 地址 **/
	String addr;
	
	/** 電話區碼 **/
	String telarea;
	
	/** 電話 **/
	String telno;
	
	public String getBranch(){
		return branch;
	}
	public void setBranch(String branch){
		this.branch = branch;
	}
	
	public String getBranchnm(){
		return branchnm;
	}
	public void setBranchnm(String branchnm){
		this.branchnm = branchnm;
	}
	
	public String getCenter(){
		return center;
	}
	public void setCenter(String center){
		this.center = center;
	}
	
	public String getCenternm(){
		return centernm;
	}
	public void setCenternm(String centernm){
		this.centernm = centernm;
	}
	
	public String getType(){
		return type;
	}
	public void setType(String type){
		this.type = type;
	}
	
	public String getNatcode(){
		return natcode;
	}
	public void setNatcode(String natcode){
		this.natcode = natcode;
	}
	
	public String getTimezone(){
		return timezone;
	}
	public void setTimezone(String timezone){
		this.timezone = timezone;
	}
	
	public String getAddr(){
		return addr;
	}
	public void setAddr(String addr){
		this.addr = addr;
	}
	
	public String getTelarea(){
		return telarea;
	}
	public void setTelarea(String telarea){
		this.telarea = telarea;
	}
	
	public String getTelno(){
		return telno;
	}
	public void setTelno(String telno){
		this.telno = telno;
	}

}
