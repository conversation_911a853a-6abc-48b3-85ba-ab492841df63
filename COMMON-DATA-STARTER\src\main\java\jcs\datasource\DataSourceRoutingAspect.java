package jcs.datasource;

import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotatedElementUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * DataSourceRoutingAspect
 *
 * 
 * 
 * 
 */
@Slf4j
@Aspect
public class DataSourceRoutingAspect {

    @Around("@within(jcs.datasource.UseDataSource) || @annotation(jcs.datasource.UseDataSource)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        String lookup = resolveLookupKey(pjp);
        if (lookup == null || lookup.isEmpty()) {
            return pjp.proceed();
        }
        if (log.isDebugEnabled()) {
            log.debug("Switch DataSource to '{}' for {}", lookup, pjp.getSignature());
        }
        DataSourceHolder.push(lookup);
        try {
            return pjp.proceed();
        } finally {
            DataSourceHolder.pop();
        }
    }

    private String resolveLookupKey(ProceedingJoinPoint pjp) {
        MethodSignature ms = (MethodSignature) pjp.getSignature();
        Method method = ms.getMethod();
        UseDataSource ann = AnnotatedElementUtils.findMergedAnnotation(method, UseDataSource.class);
        if (ann == null) {
            ann = AnnotatedElementUtils.findMergedAnnotation(ms.getDeclaringType(), UseDataSource.class);
        }
        return ann != null ? ann.value() : null;
    }
}
