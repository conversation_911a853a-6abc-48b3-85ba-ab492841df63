# Static 設定
spring.mvc.static-path-pattern=/assets/**
spring.web.resources.static-locations=classpath:/assets/
spring.web.resources.cache.period=7200

# Actuator configuration
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# JPA 選擇性設定（如果你使用 Spring Data JPA）
spring.jpa.database-platform=org.hibernate.dialect.DB2Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false

# Session 管理
server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.tracking-modes=cookie

# JSP 設定
# spring.mvc.view.prefix=/WEB-INF/jsp/
# spring.mvc.view.suffix=.jsp

# Spring Security 設定
# spring.security.user.name=admin
# spring.security.user.password=password
# spring.security.user.roles=ADMIN

# DataSource 設定
# spring.datasource.jndi-name=jdbc/eloan-ds-tw

# 初始化 SQL 腳本
# spring.sql.init.mode=always

