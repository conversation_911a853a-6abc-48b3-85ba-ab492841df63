package com.demo.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jcs.datasource.DataSourceHolder;
import jcs.datasource.UseDataSource;

import java.util.HashMap;
import java.util.Map;

/**
 * DemoDataSourceController
 *
 * 提供示範端點以展示預設資料源與以 @UseDataSource("us") 覆寫的差異。
 */
@RestController
@RequestMapping("/demo/ds")
public class DemoDataSourceController {

    /**
     * 取得目前執行緒的資料源路由鍵（未標註時為預設路由）。
     * @return 簡單地回傳目前 ThreadLocal 中的資料源鍵值（可能為 null 代表走預設）
     */
    @GetMapping("/default")
    public Map<String, Object> currentDefault() {
        Map<String, Object> resp = new HashMap<>();
        resp.put("endpoint", "/demo/ds/default");
        resp.put("currentKey", DataSourceHolder.getCustomerType());
        resp.put("note", "null 代表使用 routing 的預設資料源");
        return resp;
    }

    /**
     * 在方法層級以 @UseDataSource("us") 指定資料源；執行期間 ThreadLocal 會被 push，結束後自動 pop 還原。
     * @return 回傳目前 ThreadLocal 中的資料源鍵值（預期為 \"us\"）
     */
    @UseDataSource("us")
    @GetMapping("/us")
    public Map<String, Object> currentUs() {
        Map<String, Object> resp = new HashMap<>();
        resp.put("endpoint", "/demo/ds/us");
        resp.put("currentKey", DataSourceHolder.getCustomerType());
        resp.put("note", "此方法以 @UseDataSource(\"us\") 指定資料源");
        return resp;
    }
}

