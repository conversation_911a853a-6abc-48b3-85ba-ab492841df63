<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>tcb.jcic</groupId>
    <artifactId>tcb-ejcic-parent</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <groupId>tcb.jcic</groupId>
  <artifactId>COMMON-DATA-STARTER</artifactId>
  <version>${revision}</version>
  <name>COMMON-DATA-STARTER</name>
  <description>Spring Boot starter for common Spring Data (JPA/JDBC) configuration</description>
  <packaging>jar</packaging>
  <dependencies>
    <!-- Boot auto-configuration base -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jdbc</artifactId>
    </dependency>

    <!-- Spring Web API (for OncePerRequestFilter) -->
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>

    <!-- Servlet API provided by container in WAR apps -->
    <dependency>
      <groupId>jakarta.servlet</groupId>
      <artifactId>jakarta.servlet-api</artifactId>
      <scope>provided</scope>
    </dependency>


    <!-- Depend on COMMON-CORE for utilities (FieldUtil/SpringUtil) -->
    <dependency>
      <groupId>tcb.jcic</groupId>
      <artifactId>COMMON-CORE</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- Lombok for logging in autoconfiguration (optional) -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>

  </dependencies>
</project>