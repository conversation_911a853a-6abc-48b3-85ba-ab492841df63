package jcs.tool.generator;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.Scanner;
import java.util.StringJoiner;

import org.apache.commons.lang3.StringUtils;

import jcs.tool.exec.Tool;
import jcs.tool.bean.ColumBean;
import jcs.tool.bean.TableBean;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成Orm物件。
 */
public class OrmGenerator extends Tool {
	// 套件路徑
	private static final String classpath = "com.bean";

	public static void main(String[] args) {
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableupdate, args);
		for (WorkBean bean : workList) {
			Integer count = 0;
			System.out.println("\n");
			System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
			System.out.println("🈠 開始執行 " + bean.getName() + "『OrmGenerator』轉換");
			String from = getWorkRoot() + "✿DOCUMENT\\1、Database Design\\" + bean.getName() + "\\"; // Doc檔案來源
			String to = getWorkRoot() + bean.getProject() + "-JPA\\src\\main\\java\\com\\bean\\"; // Bean產出位置
			File folder = new File(from);
			File[] files = folder.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isDirectory()) {
						File[] files2 = file.listFiles();
						if (files2 != null) {
							for (File file2 : files2) {
								if (Tool.isCreate(file2)) {
									TableBean tablebean = new TableBean().parse(
											from + file.getName() + "\\" + file2.getName(), to, TableBean.Orm,
											file.getName());
									// 產檔-------------------------------------------------------
									new OrmGenerator().create(tablebean);
									if ("True".equals(tablebean.getHistory())) {
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname("H" + ename);
										tablebean.setCname(cname + "(歷史)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname("V" + ename);
										tablebean.setCname(cname + "(視圖)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									} else if ("Approved".equals(tablebean.getHistory())) {
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname(ename + "M");
										tablebean.setCname(cname + "(核定檔)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									}
									// 專案參照-----------------------------------------------------
									for (String references : tablebean.getReferenceslist()) {
										File referencesPath = new File(
												getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\bean\\");
										if (referencesPath.exists()) {
											tablebean.setTo(getWorkRoot() + references
													+ "-JPA\\src\\main\\java\\com\\bean\\");
											new OrmGenerator().create(tablebean);
											if ("True".equals(tablebean.getHistory())) {
												String ename = tablebean.getEname();
												String cname = tablebean.getCname();
												tablebean.setEname("H" + ename);
												tablebean.setCname(cname + "(歷史)");
												new OrmGenerator().create(tablebean);
												tablebean.setEname("V" + ename);
												tablebean.setCname(cname + "(視圖)");
												new OrmGenerator().create(tablebean);
												tablebean.setEname(ename);
												tablebean.setCname(cname);
											} else if ("Approved".equals(tablebean.getHistory())) {
												String ename = tablebean.getEname();
												String cname = tablebean.getCname();
												tablebean.setEname(ename + "M");
												tablebean.setCname(cname + "(核定檔)");
												new OrmGenerator().create(tablebean);
												tablebean.setEname(ename);
												tablebean.setCname(cname);
											}
											tablebean.setTo(to);
										} else {
											System.out.println("● 產製失敗 ●");
											System.out.println("系統找不到指定的路徑：" + referencesPath);
										}
									}
									count++;
								}
							}
						}
					} else {
						if (Tool.isCreate(file)) {
							TableBean tablebean = new TableBean().parse(from + file.getName(), to, TableBean.Orm,
									file.getName());
							// 產檔-------------------------------------------------------
							new OrmGenerator().create(tablebean);
							if ("True".equals(tablebean.getHistory())) {
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname("H" + ename);
								tablebean.setCname(cname + "(歷史)");
								new OrmGenerator().create(tablebean);
								tablebean.setEname("V" + ename);
								tablebean.setCname(cname + "(視圖)");
								new OrmGenerator().create(tablebean);
								tablebean.setEname(ename);
								tablebean.setCname(cname);
							} else if ("Approved".equals(tablebean.getHistory())) {
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname(ename + "M");
								tablebean.setCname(cname + "(核定檔)");
								new OrmGenerator().create(tablebean);
								tablebean.setEname(ename);
								tablebean.setCname(cname);
							}
							// 專案參照-----------------------------------------------------
							for (String references : tablebean.getReferenceslist()) {
								File referencesPath = new File(
										getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\bean\\");
								if (referencesPath.exists()) {
									tablebean.setTo(
											getWorkRoot() + references + "-JPA\\src\\main\\java\\com\\bean\\");
									new OrmGenerator().create(tablebean);
									if ("True".equals(tablebean.getHistory())) {
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname("H" + ename);
										tablebean.setCname(cname + "(歷史)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname("V" + ename);
										tablebean.setCname(cname + "(視圖)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									} else if ("Approved".equals(tablebean.getHistory())) {
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname(ename + "M");
										tablebean.setCname(cname + "(核定檔)");
										new OrmGenerator().create(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									}
									tablebean.setTo(to);
								} else {
									System.out.println("● 產製失敗 ●");
									System.out.println("系統找不到指定的路徑：" + referencesPath);
								}
							}
							count++;
						}
					}
				}

			}
			System.out.println("🈡 執行結果 " + bean.getName() + "共轉換 " + count + "個");
			System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
		}
		sc.close();
	}

	/**
	 * 產出Orm物件。
	 */
	public void create(TableBean tablebean) {
		FileWriter fw = null;
		try {
			StringJoiner sb1 = new StringJoiner(", ");
			StringBuffer sb2 = new StringBuffer();
			for (ColumBean columbean : tablebean.getKeyColumlist()) {
				if (columbean.getType().contains("CHAR") || columbean.getType().contains("CLOB")) {
					sb1.add("String " + columbean.getEname());
				} else if (columbean.getType().contains("BLOB")) {
					sb1.add("byte[] " + columbean.getEname());
				} else if (columbean.getType().contains("DATE") || columbean.getType().contains("TIMESTAMP")) {
					sb1.add("Date " + columbean.getEname());
				} else if (columbean.getType().contains("INTEGER")) {
					sb1.add("Integer " + columbean.getEname());
				} else if (columbean.getType().contains("DECIMAL")) {
					String type = null;
					if (columbean.getType().contains(",")) {
						String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						String[] digits = decimalType.split(",");
						type = (Integer.parseInt(digits[0]) - Integer.parseInt(digits[1])) > 7 ? "BigDecimal"
								: "Double";
					} else {
						String size = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						if (Integer.parseInt(size) > 18) {
							throw new Exception("超過最大位數，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
						} else {
							type = (Integer.parseInt(size) >= 10) ? "Long" : "Integer";
						}
					}
					sb1.add(type + " " + columbean.getEname());
				} else {
					throw new Exception("發生無法判斷，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
				}
				sb2.append("		this.").append(columbean.getEname()).append(" = ");
				sb2.append(columbean.getEname()).append(";\n");
			}
			fw = new FileWriter(tablebean.getTo() + tablebean.getOrmName() + ".java");
			// Package-----------------------------------------------------------------------------------
			fw.write("package " + classpath + ";\n\n");
			// Import------------------------------------------------------------------------------------
			fw.write(tablebean.getKeyColumlist().size() > 0 ? "import java.io.Serializable;\n\n" : "");
			fw.write("Y".equals(tablebean.getBigDecimalColum()) ? "import java.math.BigDecimal;\n\n" : "");
			fw.write("Y".equals(tablebean.getDateColum()) ? "import java.util.Date;\n\n" : "");
			fw.write("import javax.persistence.*;\n\n");
			fw.write("Y".equals(tablebean.getNulltoSpaceColum()) ? "import jcs.annotation.NullToSpace;\n" : "");
			fw.write("import jcs.beans.JcsSerialBean;\n\n");
			fw.write("\n");
			// Bean--------------------------------------------------------------------------------------
			fw.write("/**\n");
			fw.write(" * " + tablebean.getCname() + "。\n");
			fw.write(" */\n");
			fw.write("@Entity\n");
			fw.write("@Table(name = \"" + tablebean.getEname() + "\")\n");
			fw.write(tablebean.getKeyColumlist().size() > 0 ? "@IdClass(" + tablebean.getOrmName() + ".Key.class)\n"
					: "");
			fw.write("public class " + tablebean.getOrmName() + " extends JcsSerialBean{\n");
			fw.write("	private static final long serialVersionUID = 1L;\n	\n");
			// Constructor--------------------------------------------------------------------------------
			fw.write("	/** 建構子 */\n");
			fw.write("	public " + tablebean.getOrmName() + "(){}\n	\n");
			// 沒有KEY的情況下，無參數的建構子會重複
			if (StringUtils.isNotBlank(sb1.toString())) {
				fw.write("	/** 建構子 */\n");
				fw.write("	public " + tablebean.getOrmName() + "(" + sb1.toString() + "){\n");
				fw.write(sb2.toString());
				fw.write("	}\n	\n");
			}
			// Object-------------------------------------------------------------------------------------
			for (ColumBean columbean : tablebean.getColumlist()) {
				fw.write("	/** " + columbean.getCname() + " " + columbean.getType() + " */\n");
				StringBuilder column = new StringBuilder();
				column.append("	@Column(name = \"").append(columbean.getEname().toUpperCase(Locale.getDefault()))
						.append("\"");
				column.append(", columnDefinition = \"").append(columbean.getType()).append("\"");
				if (columbean.getType().contains("DECIMAL")) {
					String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
							columbean.getType().indexOf(")"));
					if (columbean.getType().contains(",")) {
						String[] digits = decimalType.split(",");
						column.append(", precision = ").append(Integer.parseInt(digits[0]));
						column.append(", scale = ").append(Integer.parseInt(digits[1]));
					} else {
						column.append(", precision = ").append(Integer.parseInt(decimalType));
					}
				}
				if ("Y".equals(columbean.getKey())) {
					fw.write("	@Id\n");
					column.append(", nullable = false");
				}
				column.append(")\n");
				if ("Y".equals(columbean.getNotnull())) {
					fw.write("	@NullToSpace\n");
				}
				fw.write(column.toString());
				if (columbean.getType().contains("CHAR") || columbean.getType().contains("CLOB")) {
					fw.write("	private String " + columbean.getEname() + ";\n");
				} else if (columbean.getType().contains("BLOB")) {
					fw.write("	private byte[] " + columbean.getEname() + ";\n");
				} else if (columbean.getType().contains("DATE") || columbean.getType().contains("TIMESTAMP")) {
					fw.write("	private Date " + columbean.getEname() + ";\n");
				} else if (columbean.getType().contains("INTEGER")) {
					fw.write("	private Integer " + columbean.getEname() + ";\n");
				} else if (columbean.getType().contains("DECIMAL")) {
					String type = null;
					if (columbean.getType().contains(",")) {
						String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						String[] digits = decimalType.split(",");
						type = (Integer.parseInt(digits[0]) - Integer.parseInt(digits[1])) > 7 ? "BigDecimal"
								: "Double";
					} else {
						String size = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						if (Integer.parseInt(size) > 18) {
							throw new Exception("超過最大位數，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
						} else {
							type = (Integer.parseInt(size) >= 10) ? "Long" : "Integer";
						}
					}
					fw.write("	private " + type + " " + columbean.getEname() + ";\n");
				} else {
					throw new Exception("發生無法判斷，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
				}
				fw.write("	\n");
			}
			// Get
			// Set-----------------------------------------------------------------------------------
			for (ColumBean columbean : tablebean.getColumlist()) {
				if (columbean.getType().contains("CHAR") || columbean.getType().contains("CLOB")) {
					fw.write("	/** get" + columbean.getCname() + " */\n");
					fw.write("	public String get"
							+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1) + "(){\n");
					fw.write("		return this." + columbean.getEname() + ";\n");
					fw.write("	}\n");
					fw.write("	\n");
					fw.write("	/** set" + columbean.getCname() + " */\n");
					fw.write("	public void set" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1));
					fw.write("(String value){\n");
					if ("Y".equals(columbean.getNotnull())) {
						fw.write("		this." + columbean.getEname() + " = (value == null ? \"\" : value);\n");
					} else {
						fw.write("		this." + columbean.getEname() + " = value;\n");
					}
					fw.write("	}\n");
				} else if (columbean.getType().contains("BLOB")) {
					fw.write("	/** get" + columbean.getCname() + " */\n");
					fw.write("	public byte[] get"
							+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1) + "(){\n");
					fw.write("		return this." + columbean.getEname() + ";\n");
					fw.write("	}\n");
					fw.write("	\n");
					fw.write("	/** set" + columbean.getCname() + " */\n");
					fw.write("	public void set" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1));
					fw.write("(byte[] value){\n");
					fw.write("		this." + columbean.getEname() + " = value;\n");
					fw.write("	}\n");
				} else if (columbean.getType().contains("DATE") || columbean.getType().contains("TIMESTAMP")) {
					fw.write("	/** get" + columbean.getCname() + " */\n");
					fw.write("	public Date get" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1) + "(){\n");
					fw.write("		return this." + columbean.getEname() + ";\n");
					fw.write("	}\n");
					fw.write("	\n");
					fw.write("	/** set" + columbean.getCname() + " */\n");
					fw.write("	public void set" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1));
					fw.write("(Date value){\n");
					fw.write("		this." + columbean.getEname() + " = value;\n");
					fw.write("	}\n");
				} else if (columbean.getType().contains("INTEGER")) {
					fw.write("	/** get" + columbean.getCname() + " */\n");
					fw.write("	public Integer get"
							+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1) + "(){\n");
					fw.write("		return this." + columbean.getEname() + ";\n");
					fw.write("	}\n");
					fw.write("	\n");
					fw.write("	/** set" + columbean.getCname() + " */\n");
					fw.write("	public void set" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1));
					fw.write("(Integer value){\n");
					fw.write("		this." + columbean.getEname() + " = value;\n");
					fw.write("	}\n");
				} else if (columbean.getType().contains("DECIMAL")) {
					String type = null;
					if (columbean.getType().contains(",")) {
						String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						String[] digits = decimalType.split(",");
						type = (Integer.parseInt(digits[0]) - Integer.parseInt(digits[1])) > 7 ? "BigDecimal"
								: "Double";
					} else {
						String size = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
								columbean.getType().indexOf(")"));
						if (Integer.parseInt(size) > 18) {
							throw new Exception("超過最大位數，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
						} else {
							type = (Integer.parseInt(size) >= 10) ? "Long" : "Integer";
						}
					}
					fw.write("	/** get" + columbean.getCname() + " */\n");
					fw.write("	public " + type + " get"
							+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1) + "(){\n");
					fw.write("		return this." + columbean.getEname() + ";\n");
					fw.write("	}\n");
					fw.write("	\n");
					fw.write("	/** set" + columbean.getCname() + " */\n");
					fw.write("	public void set" + columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
							+ columbean.getEname().substring(1));
					fw.write("(" + type + " value){\n");
					fw.write("		this." + columbean.getEname() + " = value;\n");
					fw.write("	}\n");
				} else {
					throw new Exception("發生無法判斷，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
				}
				fw.write("	\n");
			}
			// Key----------------------------------------------------------------------------------------
			if (tablebean.getKeyColumlist().size() > 0) {
				fw.write("	/** Key */\n");
				fw.write("	public static class Key implements Serializable{\n");
				fw.write("		private static final long serialVersionUID = 1L;\n		\n");
				fw.write("		public Key(){}\n		\n");
				fw.write("		public Key(" + sb1.toString() + "){\n	");
				fw.write(sb2.toString().replaceAll("\n", "\n	"));
				fw.write("	}\n		\n");
				for (ColumBean columbean : tablebean.getKeyColumlist()) {
					fw.write("		/** " + columbean.getCname() + " */\n");
					if (columbean.getType().contains("CHAR") || columbean.getType().contains("CLOB")) {
						fw.write("		private String " + columbean.getEname() + ";\n		\n");
					} else if (columbean.getType().contains("BLOB")) {
						fw.write("		private byte[] " + columbean.getEname() + ";\n		\n");
					} else if (columbean.getType().contains("DATE") || columbean.getType().contains("TIMESTAMP")) {
						fw.write("		private Date " + columbean.getEname() + ";\n		\n");
					} else if (columbean.getType().contains("INTEGER")) {
						String type = "Integer";
						fw.write("		private " + type + " " + columbean.getEname() + ";\n		\n");
					} else if (columbean.getType().contains("DECIMAL")) {
						String type = null;
						if (columbean.getType().contains(",")) {
							String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
									columbean.getType().indexOf(")"));
							String[] digits = decimalType.split(",");
							type = (Integer.parseInt(digits[0]) - Integer.parseInt(digits[1])) > 7 ? "BigDecimal"
									: "Double";
						} else {
							String size = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
									columbean.getType().indexOf(")"));
							if (Integer.parseInt(size) > 18) {
								throw new Exception("超過最大位數，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
							} else {
								type = (Integer.parseInt(size) >= 10) ? "Long" : "Integer";
							}
						}
						fw.write("		private " + type + " " + columbean.getEname() + ";\n		\n");
					} else {
						throw new Exception("發生無法判斷，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
					}
				}
				for (ColumBean columbean : tablebean.getKeyColumlist()) {
					if (columbean.getType().contains("CHAR") || columbean.getType().contains("CLOB")) {
						fw.write("		public String get"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1) + "(){\n");
						fw.write("			return this." + columbean.getEname() + ";\n");
						fw.write("		}\n");
						fw.write("		\n");
						fw.write("		public void set"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1));
						fw.write("(String value){\n");
						fw.write("			this." + columbean.getEname() + " = value;\n");
						fw.write("		}\n		\n");
					} else if (columbean.getType().contains("BLOB")) {
						fw.write("		public byte[] get"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1) + "(){\n");
						fw.write("			return this." + columbean.getEname() + ";\n");
						fw.write("		}\n");
						fw.write("		\n");
						fw.write("		public void set"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1));
						fw.write("(byte[] value){\n");
						fw.write("			this." + columbean.getEname() + " = value;\n");
						fw.write("		}\n		\n");
					} else if (columbean.getType().contains("DATE") || columbean.getType().contains("TIMESTAMP")) {
						fw.write("		public Date get"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1) + "(){\n");
						fw.write("			return this." + columbean.getEname() + ";\n");
						fw.write("		}\n");
						fw.write("		\n");
						fw.write("		public void set"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1));
						fw.write("(Date value){\n");
						fw.write("			this." + columbean.getEname() + " = value;\n");
						fw.write("		}\n		\n");
					} else if (columbean.getType().contains("INTEGER")) {
						String type = "Integer";
						fw.write("		public " + type + " get"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1) + "(){\n");
						fw.write("			return this." + columbean.getEname() + ";\n");
						fw.write("		}\n");
						fw.write("		\n");
						fw.write("		public void set"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1));
						fw.write("(" + type + " value){\n");
						fw.write("			this." + columbean.getEname() + " = value;\n");
						fw.write("		}\n		\n");
					} else if (columbean.getType().contains("DECIMAL")) {
						String type = null;
						if (columbean.getType().contains(",")) {
							String decimalType = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
									columbean.getType().indexOf(")"));
							String[] digits = decimalType.split(",");
							type = (Integer.parseInt(digits[0]) - Integer.parseInt(digits[1])) > 7 ? "BigDecimal"
									: "Double";
						} else {
							String size = columbean.getType().substring(columbean.getType().indexOf("(") + 1,
									columbean.getType().indexOf(")"));
							if (Integer.parseInt(size) > 18) {
								throw new Exception("超過最大位數，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
							} else {
								type = (Integer.parseInt(size) >= 10) ? "Long" : "Integer";
							}
						}
						fw.write("		public " + type + " get"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1) + "(){\n");
						fw.write("			return this." + columbean.getEname() + ";\n");
						fw.write("		}\n");
						fw.write("		\n");
						fw.write("		public void set"
								+ columbean.getEname().substring(0, 1).toUpperCase(Locale.getDefault())
								+ columbean.getEname().substring(1));
						fw.write("(" + type + " value){\n");
						fw.write("			this." + columbean.getEname() + " = value;\n");
						fw.write("		}\n		\n");
					} else {
						throw new Exception("發生無法判斷，欄位：" + columbean.getEname() + "類別：" + columbean.getType());
					}
				}
				fw.write("		@Override\n");
				fw.write("		public int hashCode(){\n");
				fw.write("			final int prime = 31;\n");
				fw.write("			int result = 1;\n");
				for (ColumBean columbean : tablebean.getKeyColumlist()) {
					fw.write("			result = prime * result + ((" + columbean.getEname() + " == null) ? 0 : "
							+ columbean.getEname() + ".hashCode());\n");
				}
				fw.write("			return result;\n");
				fw.write("		}\n		\n");
				fw.write("		@Override\n");
				fw.write("		public boolean equals(Object obj){\n");
				fw.write("			if(this == obj)\n");
				fw.write("				return true;\n");
				fw.write("			if(obj == null)\n");
				fw.write("				return false;\n");
				fw.write("			if(getClass() != obj.getClass())\n");
				fw.write("				return false;\n");
				fw.write("			Key other = (Key)obj;\n");
				for (ColumBean columbean : tablebean.getKeyColumlist()) {
					fw.write("			if(" + columbean.getEname() + " == null){\n");
					fw.write("				if(other." + columbean.getEname() + " != null)\n");
					fw.write("					return false;\n");
					fw.write("			}else if(!" + columbean.getEname() + ".equals(other." + columbean.getEname()
							+ "))\n");
					fw.write("				return false;\n");
				}
				fw.write("			return true;\n");
				fw.write("		}\n");
				fw.write("	}\n");
			}
			fw.write("}\n");
			fw.flush();
			System.out.println("● 產製成功 ●");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (fw != null)
					fw.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
