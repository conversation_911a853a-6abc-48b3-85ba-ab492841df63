package jcs.tool.generator;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Scanner;
import java.util.Set;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import jcs.tool.exec.Tool;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成系統常數。
 */
public class ProjectConstantGenerator extends Tool{
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, projectconstant, args);
		new ProjectConstantGenerator().create(workList.get(0), getItem());
	}
	
	/** 產出系統常數 */
	public void create(WorkBean workBean, String item){
		System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
		System.out.println("🈠 開始執行 "+ workBean.getProject() +"『ProjectConstantGenerator』轉換");
		switch(item){
			case "1":
				xlsxToJava(workBean);
				break;
		}
		System.out.println("🈡 執行完成");
		System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
	}
	
	/**
	 * XLSX轉換為JAVA。
	 * 
	 * @param workBean
	 */
	private static void xlsxToJava(WorkBean workBean){
		Workbook wb = null;
		InputStream in = null;
		FileWriter fw = null;
		StringBuilder path = new StringBuilder();
		StringBuilder constant = new StringBuilder();
		StringBuilder importStr = new StringBuilder("import org.springframework.beans.factory.annotation.Value;\n\n");
		boolean annotation = false;
		constant.append("package com.system.constant;").append("\n");
		constant.append("\n");
		constant.append(importStr.toString());
		constant.append("/**").append("\n");
		constant.append(" * 系統常數。").append("\n");
		constant.append(" */").append("\n");
		constant.append("public interface ProjectConstant extends BankConstant{");
		path.append(getWorkRoot()).append("Tool");
		path.append(File.separator).append("src");
		path.append(File.separator).append("jcs");
		path.append(File.separator).append("tool");
		path.append(File.separator).append(workBean.getProject());
		path.append("_ProjectConstant.xlsx");
		File xlsx = new File(path.toString());
		Set<String> names = new HashSet<String>();
		try{
			in = new FileInputStream(xlsx);
			wb = WorkbookFactory.create(in);
			Sheet sheet = wb.getSheetAt(0);
			for(Row row : sheet){
				int rowIndex = row.getRowNum();
				if(rowIndex > 0){
					String name = row.getCell(0).toString();
					String key = row.getCell(1).toString();
					String value = row.getCell(2).toString();
					String display = row.getCell(3) == null ? null : row.getCell(3).toString();
					if(!name.isEmpty()){
						if(!names.contains(name)){
							if(names.size() > 0){
								constant.append("	}").append("\n");
							}else{
								constant.append("\n");
							}
							constant.append("\n");
							constant.append("	/** ").append(key).append(" **/").append("\n");
							constant.append("	interface ").append(name).append(" {");
							constant.append("\n");
							names.add(name);
						}else{
							if(display != null && display.trim().length() > 0){
								constant.append("		@Value(\"").append(display).append("\")");
								constant.append("String ").append(value).append(" = \"").append(key).append("\";");
								constant.append("\n");
								annotation = true;
							}else{
								constant.append("		String ").append(value).append(" = \"").append(key).append("\";");
								constant.append("\n");
							}
						}
					}
				}
			}
			constant.append("	}").append("\n");
			constant.append("}");
			path.delete(0, path.length());
			path.append(getWorkRoot()).append(workBean.getProject());
			path.append(File.separator).append("src");
			path.append(File.separator).append("main");
			path.append(File.separator).append("java");
			path.append(File.separator).append("com");
			path.append(File.separator).append("system");
			path.append(File.separator).append("constant");
			path.append(File.separator).append("ProjectConstant.java");
			System.out.println(path.toString());
			File java = new File(path.toString());
			if(java.exists())
				java.delete();
			java.getParentFile().mkdirs();
			fw = new FileWriter(path.toString());
			fw.write(annotation == false ? 
					constant.toString().replaceAll(importStr.toString(), "") : constant.toString());
			fw.flush();
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(wb != null)
					wb.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(in != null)
					in.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}
}
