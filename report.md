# 任務報告（協調者模式）

## 摘要
- 依「方案 1：多個精細化 Starter」執行第一階段落地：建立 COMMON-CORE / COMMON-WEB-STARTER / COMMON-DATA-STARTER 三個模組，並更新 root pom.xml 的 <modules>。
- 已完成 Web Phase 1.2：刪除 COMMON-JCS 的 WebConfig，改由 COMMON-WEB-STARTER 自動組態接手；
- 已啟動 Data Phase 2：將 jcs.repository.CustomJpaDao 搬遷至 COMMON-DATA-STARTER，並於 EJCIC-JPA / EJMN-JPA / ETCH-JPA 加入對 COMMON-DATA-STARTER 的依賴；現已將 SpringUtil/FieldUtil 移至 COMMON-CORE，並移除 DATA-STARTER 	-> COMMON-JCS 的暫時依賴（完成 Phase 2.2 去耦）。

## 已完成子任務
1) 以 PowerShell 建立新模組目錄結構
2) 新增各模組 pom.xml：
   - COMMON-WEB-STARTER：依賴 spring-boot-autoconfigure、spring-boot-starter-web（排除 tomcat）、spring-boot-starter-freemarker、sitemesh、jakarta.servlet-api（provided）
   - COMMON-DATA-STARTER：依賴 spring-boot-autoconfigure、spring-boot-starter-data-jpa、spring-boot-starter-data-jdbc，並依賴 COMMON-CORE（提供工具類別 FieldUtil / SpringUtil）
   - COMMON-CORE：新增依賴 commons-beanutils、commons-lang、spring-context、spring-orm、spring-web、jakarta.persistence-api、slf4j-api（支援工具類別運作）
3) 新增 AutoConfiguration 類別與 imports：
   - COMMON-WEB-STARTER：CommonWebAutoConfiguration（提供預設 WebMvcConfigurer Bean）
   - COMMON-DATA-STARTER：DataAutoConfiguration（提供標記用 Bean）
4) 更新 root pom.xml <modules>，加入三個新模組
5) 建置驗證：`mvn -B -DskipTests package` 成功（全部 13 模組 SUCCESS）

## 重要決策
- 保持 COMMON-JCS 現狀，先以 Starter 骨架鋪設遷移跑道；後續再逐步搬移 Web/Data 組態，避免一次性大變更導致風險。
- Web Starter 排除 Tomcat，維持與 Liberty 容器相容；Servlet API 於 WAR 層以 provided 提供。

## 待辦與風險
- 待辦：把 COMMON-JCS 內 Web/Data 組態遷入對應 Starter，並以 Conditional 設計確保可覆寫。
- 風險：搬移過程需逐一比對行為；需在各 WAR 應用啟用 Starter 後做回歸驗證。

## 指令記錄（PowerShell）
- 目錄建立：
  - New-Item -ItemType Directory -Force -Path 'COMMON-CORE\\src\\main\\java\\jcs\\core', ...（多路徑）
- 建置驗證：
  - mvn -B -DskipTests package

## 建議下一步
- 依 todolist.md 逐項推進：
  - [完成] Phase 1.2：Web 組態移轉已完成，刪除 COMMON-JCS 的 WebConfig，Starter 接手
  - [完成] Phase 2.2：已將 SpringUtil/FieldUtil 移至 COMMON-CORE，並以 COMMON-CORE 供應給 DATA-STARTER；已移除 DATA-STARTER -> COMMON-JCS 暫時依賴
  - 各 WAR 模組已加入 COMMON-WEB-STARTER 依賴；Data 組態待完成後做行為比對
  - 通過驗證後，縮減 COMMON-JCS 的 Boot/Web/Data 依賴（最終保留純工具或移至 COMMON-CORE）

## Checkpoint 建議（尚未提交）
- refactor(web): remove COMMON-JCS WebConfig and rely on COMMON-WEB-STARTER autoconfig
- chore(app): wire COMMON-WEB-STARTER in EJCIC/EJMN/ETCH (already added)
- refactor(data): move CustomJpaDao to COMMON-DATA-STARTER and add *-JPA dependency
- refactor(core): move FieldUtil/SpringUtil to COMMON-CORE and add required deps
- chore(data-starter): replace temporary COMMON-JCS dependency with COMMON-CORE
- chore(jcs): add COMMON-CORE dependency for shared utilities
- docs: update spec/todolist/report to reflect Data Phase 2.2 completion

## 本輪更新（Phase 2.3 + Lombok 日誌）
- 已新增 DataRoutingAutoConfiguration（COMMON-DATA-STARTER）：
  - 以屬性 jcs.datasource.routing.enabled=true 啟用
  - 自動聚合多個 DataSource Bean 為目標清單，並以第一個為 defaultTarget
  - 採用 AbstractRoutingDataSource 匿名子類，透過 ThreadLocal（DataSourceHolder）決定路由鍵
- 導入 Lombok 並調整日誌：
  - COMMON-CORE 的 FieldUtil 改用 @Slf4j
  - COMMON-DATA-STARTER 的自動組態類（DataRoutingAutoConfiguration）改用 @Slf4j
  - 全專案：統一改用 @Slf4j 並移除手動 Logger（涵蓋 COMMON-JCS、COMMON-WEB-STARTER、EJCIC/EJMN/ETCH 模組內 Controller/Security/Interceptor 等類別）
  - POM 依賴：於 COMMON-JCS、COMMON-WEB-STARTER、EJCIC、EJMN、ETCH 加入 Lombok（scope=provided, optional=true）
- 建置驗證：mvn -B -DskipTests package → BUILD SUCCESS（13 模組）

## Phase 2.4：清理重複 DataSource 類別（完成）
- 從 COMMON-JCS 完整移除 `jcs.datasource.DataSourceHolder` / `RoutingDataSource`，避免重複
- 將 EJCIC/EJMN/ETCH 的 DataSourceConfig 改為僅宣告具名 JNDI DataSource（如 tw/us），動態路由交由 DATA-STARTER 提供
- 在 EJCIC/EJMN/ETCH pom.xml 新增依賴：COMMON-DATA-STARTER
- DATA-STARTER 的 routingDataSource Bean 改為 `@Primary`，確保多資料來源時預設使用路由資料源
- 建置驗證：mvn -B -DskipTests package → BUILD SUCCESS（13 模組）


## 版本資訊查詢（Lombok）
- 查詢時間：2025-09-09（以 Context7 即時文件檢索）
- 來源：/projectlombok/lombok 官方文件片段（Maven/Gradle 設定、annotationProcessorPaths 指南）
- 最新穩定版本號：本次檢索片段未直接提供明確版本號（多為占位 ${version} 或 edge-SNAPSHOT 範例），因此無法保證準確版本號
  - 臨時建議：維持目前使用的 1.18.36 版本（可能過時）；如需最準確版本，請於發版前再次透過 Context7 或官網下載頁確認
- 相容性與設定：
  - JDK 21：一般情況下無需在 maven-compiler-plugin 額外設定 annotationProcessorPaths
  - JDK 23 或使用 module-info.java：需於 maven-compiler-plugin 的 configuration.annotationProcessorPaths 指定 lombok 版本以啟用處理器（官方文件明確要求）
- 配置語法變更：無重大變更；但在 JDK 23 之後，未正確宣告 annotationProcessorPaths 會導致 Lombok 標註未被處理
- 已知問題與解法：
  - 編譯輸出看到 “Annotation processing is enabled…” 提示屬正常現象；若 IDE 顯示無法解析 @Slf4j，請確認模組 POM 已加入 lombok provided 依賴並重新載入專案
- 升級建議：
  - 先以 Context7 查核最新版，再同步 root pom 中的 lombok.version 屬性，並於所有使用 Lombok 的模組保持 scope=provided，必要時在 maven-compiler-plugin 增補 annotationProcessorPaths

## Checkpoint 建議（追加）
- feat(data-starter): add DataRoutingAutoConfiguration for conditional routing datasource
- refactor(core): switch FieldUtil logger to Lombok @Slf4j
- refactor(logging): convert manual SLF4J loggers to Lombok @Slf4j across project
- chore(deps): add Lombok to COMMON-JCS/COMMON-WEB-STARTER/EJCIC/EJMN/ETCH (scope=provided)
- docs: update spec/todolist/report for Phase 2.3 and Lombok logging adoption


