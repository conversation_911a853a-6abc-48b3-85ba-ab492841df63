package jcs.autoconfigure;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import lombok.extern.slf4j.Slf4j;

/**
 * DataSource 動態路由的自動組態。
 *
 * 提供在屬性 jcs.datasource.routing.enabled=true 時，自動註冊一個名稱為
 * "routingDataSource" 的資料來源，將應用程式中其餘 DataSource Bean
 * 聚合為 targetDataSources，並從第一個可用的 DataSource 作為 defaultTarget。
 *
 * 注意：
 * - 若應用端已自定義名為 routingDataSource 的 Bean，則不會覆寫（ConditionalOnMissingBean）。
 * - 若應用端僅有一個 DataSource，則不建立路由（避免多此一舉）。
 * - 需要應用端先宣告具名的多個 DataSource Bean（例如 primary、secondary 等）。
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass(AbstractRoutingDataSource.class)
public class DataRoutingAutoConfiguration {

    /**
     * 建立 RoutingDataSource。
     *
     * @param allDataSources ObjectProvider 取得所有現有的 DataSource Bean（名稱->Bean）
     * @return DataSource（RoutingDataSource），或在條件不符時不建立
     */
    @Primary
    @Bean(name = "routingDataSource")
    @ConditionalOnProperty(prefix = "jcs.datasource.routing", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "routingDataSource")
    public DataSource routingDataSource(ObjectProvider<Map<String, DataSource>> allDataSources,
            DataSourceJndiProperties props) {
        Map<String, DataSource> dsMap = allDataSources.getIfAvailable(LinkedHashMap::new);
        if (dsMap == null || dsMap.size() <= 1) {
            log.debug("Skip RoutingDataSource autoconfig: dataSourceCount={}", (dsMap == null ? 0 : dsMap.size()));
            return null; // 只有 0 或 1 個資料來源時不建立路由
        }

        // 避免把自己（routingDataSource）放進目標清單
        Map<Object, Object> targets = dsMap.entrySet().stream()
                .filter(e -> !"routingDataSource".equals(e.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a, LinkedHashMap::new));

        if (targets.isEmpty()) {
            log.warn("RoutingDataSource autoconfig found only self or empty targets, skip.");
            return null;
        }

        AbstractRoutingDataSource routing = new AbstractRoutingDataSource() {
            @Override
            protected Object determineCurrentLookupKey() {
                return jcs.datasource.DataSourceHolder.getCustomerType();
            }
        };
        routing.setTargetDataSources(targets);
        String preferred = (props != null ? props.getDefault() : null);
        if (preferred != null && targets.containsKey(preferred)) {
            routing.setDefaultTargetDataSource(targets.get(preferred));
        } else {
            routing.setDefaultTargetDataSource(targets.values().iterator().next());
        }
        routing.afterPropertiesSet();
        log.info("RoutingDataSource autoconfig enabled: targets={}, default={}", targets.keySet(),
                (preferred != null && targets.containsKey(preferred)) ? preferred : targets.keySet().iterator().next());
        return routing;
    }
}
