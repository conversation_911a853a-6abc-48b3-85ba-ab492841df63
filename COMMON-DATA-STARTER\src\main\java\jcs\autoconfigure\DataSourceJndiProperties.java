package jcs.autoconfigure;

import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * DataSourceJndiProperties
 *
 * 以 application 屬性綁定多個 JNDI 資料來源的設定：
 * jcs.datasource.jndi.<name>=<jndiName>
 * 例如：
 * jcs.datasource.jndi.tw=jdbc/ELTW
 * jcs.datasource.jndi.us=jdbc/NJCICUSDB
 */
@ConfigurationProperties(prefix = "jcs.datasource")
public class DataSourceJndiProperties {

    /**
     * key: Bean 名稱（例如 tw、us）
     * value: JNDI 名稱（例如 jdbc/ELTW）
     */
    private Map<String, String> jndi = new LinkedHashMap<>();

    /**
     * 預設資料來源 Bean 名稱（若未指定則以宣告順序第一個為預設）。
     * 對應屬性：jcs.datasource.default
     */
    private String defaultName;

    /**
     * 取得屬性 jcs.datasource.default 的值。
     * 
     * @return 預設資料來源名稱
     */
    public String getDefault() {
        return defaultName;
    }

    /**
     * 設定屬性 jcs.datasource.default 的值。
     * 
     * @param value 預設資料來源名稱
     */
    public void setDefault(String value) {
        this.defaultName = value;
    }

    public Map<String, String> getJndi() {
        return jndi;
    }

    public void setJndi(Map<String, String> jndi) {
        this.jndi = jndi;
    }
}
