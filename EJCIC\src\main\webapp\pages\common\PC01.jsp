<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="f" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="m" uri="http://java.sun.com/jsp/jstl/fmt"%>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- 用戶資訊卡片 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user"></i> 用戶資訊</h5>
                    <button type="button" class="btn btn-outline-light btn-sm" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> 登出
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>登入用戶：</strong> 
                            </p>
                            <p><strong>用戶權限：</strong>
                                <c:forEach items="${authorities}" var="authority" varStatus="status">
                                    <span class="badge bg-secondary">${authority.authority}</span>
                                    <c:if test="${!status.last}">, </c:if>
                                </c:forEach>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>登入時間：</strong> 
                                <span id="loginTime"></span>
                            </p>
                            <p><strong>會話狀態：</strong> 
                                <span class="badge bg-success">已驗證</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 登出確認 Modal -->
<div class="modal fade" id="logoutModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">確認登出</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>您確定要登出系統嗎？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmLogout()">確認登出</button>
            </div>
        </div>
    </div>
</div>

<!-- 登出表單 (隱藏) -->
<form id="logoutForm" action="/perform_logout" method="post" style="display: none;">
</form>

<script>
   
    $(document).ready(function() {
        // 顯示登入時間
        $('#loginTime').text(new Date().toLocaleString('zh-TW'));
        
        // 卡片點擊效果
        $('.card.bg-light').hover(
            function() {
                $(this).addClass('shadow-sm').css('transform', 'translateY(-5px)');
            },
            function() {
                $(this).removeClass('shadow-sm').css('transform', 'translateY(0)');
            }
        );
        
        // 自動隱藏 alert
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 10000);
    });

    function logout() {
        $('#logoutModal').modal('show');
    }

    function confirmLogout() {
        $('#logoutModal').modal('hide');
        $('#logoutForm').submit();
    }

    // 會話檢查 (每5分鐘檢查一次會話狀態)
    setInterval(function() {
        $.ajax({
            url: '/logon',
            type: 'GET',
            success: function() {
                console.log('會話仍然有效');
            },
            error: function(xhr) {
                if (xhr.status === 401 || xhr.status === 403) {
                    alert('會話已過期，請重新登入');
                    window.location.href = '/login';
                }
            }
        });
    }, 5 * 60 * 1000); // 5分鐘
</script>
