# TODO List - Starter 重構（Plan 1）

- [x] 新增三個模組骨架（COMMON-CORE / COMMON-WEB-STARTER / COMMON-DATA-STARTER）
- [x] 將新模組加入 root pom.xml 的 <modules>
- [x] 建置驗證：`mvn -B -DskipTests package` 成功
- [x] 將 COMMON-JCS 中 Web 組態（Filter/Interceptor/ViewResolver/Freemarker/SiteMesh）搬遷到 COMMON-WEB-STARTER（加上條件註解與可覆寫設計）—已移除 COMMON-JCS 的 WebConfig，由 Starter 自動組態接手
- [x] 將 COMMON-JCS 中 Data 組態（JPA/JDBC 相關）搬遷到 COMMON-DATA-STARTER（第一步：搬移 jcs.repository.CustomJpaDao，JPA 模組加入依賴；已將 SpringUtil/FieldUtil 移至 COMMON-CORE，並移除 DATA-STARTER → COMMON-JCS 的暫時依賴）
- [x] 各 WAR 模組（EJCIC / EJMN / ETCH）加入對 COMMON-WEB-STARTER 的依賴，並存驗證成功；各 JPA 模組（EJCIC-JPA / EJMN-JPA / ETCH-JPA）已加入對 COMMON-DATA-STARTER 的依賴
- [x] Phase 2.3：加入 DataRoutingAutoConfiguration，提供條件式 RoutingDataSource 自動組態（以屬性 `jcs.datasource.routing.enabled=true` 啟用）
- [x] 導入 Lombok（@Slf4j）並將 COMMON-CORE 的 FieldUtil 與 DATA-STARTER 的自動組態使用 Lombok 日誌
- [x] 全專案：以 Lombok @Slf4j 統一日誌（移除手動 Logger），並於 COMMON-JCS / COMMON-WEB-STARTER / EJCIC / EJMN / ETCH 加入 lombok（scope=provided）

- [x] 從 COMMON-JCS 完整移除 `jcs.datasource.DataSourceHolder` / `RoutingDataSource` 類別，避免類別重複（僅由 DATA-STARTER 提供）

- [ ] 確認行為一致後，移除 COMMON-JCS 中 Boot/Web/Data 相關依賴與類別，保留純工具，或遷移至 COMMON-CORE
- [ ] 補充單元測試 / 煙霧測試，確保自動組態與覆寫邏輯正確
- [ ] 更新文件（README、spec.md、report.md）與 Conventional Commits 訊息草案

