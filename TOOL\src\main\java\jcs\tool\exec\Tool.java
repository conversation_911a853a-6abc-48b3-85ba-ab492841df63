package jcs.tool.exec;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Container;
import java.awt.Font;
import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Scanner;

import javax.swing.BorderFactory;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JOptionPane;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import jcs.tool.bean.WorkBean;

/**
 * 開發工具。
 */
public abstract class Tool{
	public static final String parmconstant = "1";
	
	public static final String projectconstant = "2";
	
	public static final String tableconstant = "3";
	
	public static final String syncsource = "4";
	
	public static final String tableupdate = "5";
	
	public static Font defFont = new Font(Font.DIALOG, Font.BOLD, 16);
	
	private static final String GOOGLE_TRANSLATE_URL = "https://translate.google.com/m?sl=zh-TW&tl=en&q=%s";
	
	@Retention(RetentionPolicy.RUNTIME)
	public @interface Path{
		String exec();
		String path();
	}
	
	/** 系統代碼 */
	interface SYSTYPE{
		@Path(exec = "1", path = "PROJECT")
		String PROJECT = "PROJECT";

	}
	
	/** 取得工作目錄 */
	public static String getWorkRoot(){
		StringBuilder workRoot = new StringBuilder();
		workRoot.append(
				Thread.currentThread().getContextClassLoader().getResource("").getPath().replaceAll("/Tool/bin/", "/"));
		return workRoot.substring(1, workRoot.length()).toString().replaceAll("/", "\\\\");
	}
	
	/** 取得工作項目 */
	public static List<WorkBean> getWork(Scanner sc, String tool, String[] args){
		List<WorkBean> result = new ArrayList<WorkBean>();
		try{
			if(args.length == 0){
				if(parmconstant.equals(tool)){
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.println("＠ 執行系統選單轉換 ＠");
					System.out.println("『執行』請輸入『Y』；『離開』請輸入『N』");
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.print("請輸入選項：");
				}else if(projectconstant.equals(tool)){
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.println("＠ 執行系統常數轉換 ＠");
					for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
						System.out.println(
								"『" + field.getName() + "』請輸入『" + field.getAnnotation(Path.class).exec() + "』");
					}
					System.out.println("『離開』請輸入『N』");
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.print("請輸入選項：");
				}else if(tableconstant.equals(tool)){
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.println("＠ 執行表格常數轉換 ＠");
					System.out.println("『執行』請輸入『Y』；『離開』請輸入『N』");
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.print("請輸入選項：");
				}else if(syncsource.equals(tool)){
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.println("＠ 執行同步程式 ＠");
					System.out.println("＠ 請選擇那個專案為複寫來源 ＠");
					for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
						System.out.println(
								"『" + field.getName() + "』請輸入『" + field.getAnnotation(Path.class).exec() + "』");
					}
					System.out.println("『離開』請輸入『N』");
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.print("請輸入選項：");
				}else if(tableupdate.equals(tool)){
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.println("＠ 執行表格異動 ＠");
					for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
						System.out.println(
								"『" + field.getName() + "』請輸入『" + field.getAnnotation(Path.class).exec() + "』");
					}
					System.out.println("『全部』請輸入『Y』；『離開』請輸入『N』");
					System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
					System.out.print("請輸入選項：");
				}
			}
			String input = sc.next();
			if(input != null && ("Y".equals(input.toUpperCase(Locale.getDefault()))
					&& (parmconstant.equals(tool) || tableconstant.equals(tool) || tableupdate.equals(tool)))){
				for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
					result.add(getWorkBean(field.getAnnotation(Path.class).exec()));
				}
			}else if(input != null){
				for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
					if(field.getAnnotation(Path.class).exec().equals(input.toUpperCase(Locale.getDefault()))){
						result.add(getWorkBean(input.toUpperCase(Locale.getDefault())));
					}
				}
			}
			if(result.size() == 0){
				System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
				System.out.println("程式結束");
				System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
				System.exit(0);
				if(sc != null)
					sc.close();
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return result;
	}
	
	/** 取得執行項目 */
	public static String getItem(){
		String item = null;
		Scanner sc = new Scanner(System.in);
		System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
		System.out.println("請選擇轉換功能");
		System.out.println("1.XLS=>JAVA");
		System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
		System.out.print("請輸入選項：");
		item = sc.next();
		if(!item.matches("^(1)$")){
			System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
			System.out.println("執行項目錯誤，程式結束");
			System.out.println("✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱✱");
			System.exit(0);
			if(sc != null)
				sc.close();
		}
		return item;
	}
	
	/** 取得專案物件 */
	public static WorkBean getWorkBean(String v){
		WorkBean result = new WorkBean();
		try{
			result.setCode(v);
			for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
				if(field.getAnnotation(Path.class).exec().equals(v)){
					result.setName(field.getAnnotation(Path.class).path());
					result.setProject(field.get(field.getName()).toString());
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return result;
	}
	
	/** 取得所有專案物件 */
	public static LinkedList<WorkBean> getAllWorkBean(){
		LinkedList<WorkBean> list = new LinkedList<WorkBean>();
		for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
			list.add(Tool.getWorkBean(field.getAnnotation(Path.class).exec()));
		}
		return list;
	}
	
	/** 判斷是否產出 */
	public static boolean isCreate(File file){
		if(isDocCreate(file) && !"X、視圖".equals(file.getParentFile().getName())){
			return true;
		}
		return false;
	}
	
	/** 判斷是否產出至表格清單 */
	public static boolean isDocCreate(File file){
		if(file.getName().contains(".doc") && !file.getName().contains(".docx") && !file.getName().startsWith("~")
				&& !file.getPath().contains("整理中")){
			return true;
		}
		return false;
	}
	
	/***
	 * ====================================================================================
	 * 以下為視窗化
	 * ====================================================================================
	 */
	private static String ppp = "....";
	
	private static String sss = "    ";
	
	private static int progressCount = 0;
	
	private static JDialog progressDialog;
	
	private static JLabel progressLabel;
	
	static void init(JFrame frame){
		// 建置處理中畫面
		progressLabel = new JLabel("", JLabel.CENTER);
		progressLabel.setFont(defFont);
		progressLabel.setBorder(BorderFactory.createLineBorder(Color.BLACK));
		//
		progressDialog = new JDialog(frame);
		progressDialog.setUndecorated(true);
		progressDialog.add(BorderLayout.CENTER, progressLabel);
		progressDialog.setDefaultCloseOperation(JDialog.DO_NOTHING_ON_CLOSE);
		progressDialog.setVisible(false);
	}
	
	/**
	 * 處理中...
	 * 
	 * @param is
	 */
	public static void processing(boolean is){
		if(is){
			// init size Location
			Container Container = progressDialog.getParent();
			progressDialog.setSize(Container.getWidth(), Container.getHeight());
			progressDialog.setLocation(Container.getLocation());
			//
			progressCount = 0;
			Thread t = new Thread(new Runnable() {
				public void run(){
					progressDialog.setVisible(true);
					while(++progressCount < 100){
						progressLabel.setText(progressing(progressCount));
						try{
							Thread.sleep(200);
						}catch(InterruptedException e){
							e.printStackTrace();
						}
					}
					progressDialog.setVisible(false);
				}
			});
			t.start();
		}else{
			progressCount = 9999;
		}
	}
	
	private static String progressing(int i){
		int p = i % ppp.length();
		return " 執行中 " + ppp.substring(0, p) + sss.substring(0, ppp.length() - p);
	}
	
	/**
	 * 警示訊息
	 * 
	 * @param msg
	 */
	public static void alert(String msg){
		JOptionPane.showMessageDialog(progressDialog.getParent(), msg);
	}
	
	/** 單詞翻譯 **/
	public static String translateWord(String word) {
		try {
			word = word.replaceAll("<BR>", "");
			word = word.replaceAll("<br>", "");
			word = word.replaceAll("<BR/>", "");
			word = word.replaceAll("<br/>", "");
			word = word.replaceAll("<BR />", "");
			word = word.replaceAll("<br />", "");
			word = word.replaceAll("　", "");
			String encodedText = URLEncoder.encode(word, StandardCharsets.UTF_8.toString());
			String url = String.format(GOOGLE_TRANSLATE_URL, encodedText);
			Document doc = Jsoup.connect(url).userAgent("Mozilla/5.0").get();
			return doc.select("div.result-container").text(); // **提取翻譯結果**
		} catch (Exception e) {
			e.printStackTrace();
			return word; // **翻譯失敗則返回原文字**
		}
	}
}
