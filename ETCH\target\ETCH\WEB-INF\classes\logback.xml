<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<jmxConfigurator />
	<property name="LOG_HOME" value="/workspace-tcb-ejcic/ACTIVITY/ETCH" />
	<property name="LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss}][%level][%4X{uid}-%replace(%t){'^.+\-',''}]%25C{1} - %m%n" />
	<property name="LOG_PATTERN_API" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}][%4X{SID}-%replace(%t){'^.+\-',''}]%25C{1} - %m%n" />
	<property name="LOG_ENCODING" value="UTF-8" />
	<property name="LOG_KEEP" value="30" />

	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${LOG_PATTERN}</pattern>
			<charset>${LOG_ENCODING}</charset>		
		</encoder>
	</appender>
	
	<appender name="debugLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_HOME}/debug.log.%d{yyyy-MM-dd}</fileNamePattern>
			<MaxHistory>${LOG_KEEP}</MaxHistory>
			<cleanHistoryOnStart>true</cleanHistoryOnStart>
		</rollingPolicy>
		<encoder>
			<pattern>${LOG_PATTERN}</pattern>
			<charset>${LOG_ENCODING}</charset>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>DEBUG</level>
		</filter>
	</appender>
	
	<!-- 列印SQL參數  -->
	<!-- 
	<logger name="org.hibernate.type" level="TRACE" additivity="false">
		<appender-ref ref="console" />
	</logger>
	 -->
	<logger name="org.hibernate.engine.jdbc.spi.SqlExceptionHelper" level="ERROR" additivity="false">
		<appender-ref ref="debugLog" />
	</logger>
	<root level="INFO">
		<appender-ref ref="console" />
		<appender-ref ref="debugLog" />
	</root>

</configuration>