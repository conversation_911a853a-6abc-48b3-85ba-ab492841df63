package jcs.tool.generator;

import java.io.File;
import java.io.FileOutputStream;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Scanner;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;

import jcs.tool.exec.Tool;
import jcs.tool.bean.TableBean;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成表格清單。
 */
public class ListGenerator extends Tool{
	// 範本檔案放置路徑
	private static final String sample = "Tool\\src\\main\\resources\\List.docx";
	// 表格清單產出路徑
	private static final String topath = "\\1、資料庫設計\\1.2、表格清單\\表格清單.docx";
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableupdate, args);
		for(WorkBean bean : workList){
			LinkedList<TableBean> tableList = new LinkedList<TableBean>();
			Integer count = 0;
			System.out.println("\n");
			System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
			System.out.println("🈠 開始執行 " + bean.getName() + "『ListGenerator』轉換");
			String from = getWorkRoot() + "DOC\\" + bean.getName() + "\\1、資料庫設計\\1.3、表格內容說明\\"; // Doc檔案來源
			String to = getWorkRoot() + "DOC\\" + bean.getName() + "\\1、資料庫設計\\1.2、表格清單\\"; // Sql產出至...
			File folder = new File(from);
			File[] files = folder.listFiles();
			if(files != null){
				for(File file : files){
					if(file.isDirectory()){
						File[] files2 = file.listFiles();
						if(files2 != null){
							for(File file2 : files2){
								if(Tool.isDocCreate(file2)){
									TableBean tablebean = new TableBean().parse(
											from + file.getName() + "\\" + file2.getName(), to, TableBean.List, file.getName());
									// 產檔-------------------------------------------------------
									tableList.add(tablebean);
									if("True".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname("H" + ename);
										tablebean.setCname(cname + "(歷史)");
										tableList.add(tablebean);
										tablebean.setEname("V" + ename);
										tablebean.setCname(cname + "(視圖)");
										tableList.add(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									} else if("Approved".equals(tablebean.getHistory())){
										String ename = tablebean.getEname();
										String cname = tablebean.getCname();
										tablebean.setEname(ename + "M");
										tablebean.setCname(cname + "(核定檔)");
										tableList.add(tablebean);
										tablebean.setEname(ename);
										tablebean.setCname(cname);
									}
									count++;
								}
							}
						}
					}else{
						if(Tool.isCreate(file)){
							TableBean tablebean = new TableBean().parse(
									from + file.getName(), to, TableBean.List, file.getName());
							// 產檔-------------------------------------------------------
							tableList.add(tablebean);
							if("True".equals(tablebean.getHistory())){
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname("H" + ename);
								tablebean.setCname(cname + "(歷史)");
								tableList.add(tablebean);
								tablebean.setEname("V" + ename);
								tablebean.setCname(cname + "(視圖)");
								tableList.add(tablebean);
								tablebean.setEname(ename);
								tablebean.setCname(cname);
							}else if("Approved".equals(tablebean.getHistory())){
								String ename = tablebean.getEname();
								String cname = tablebean.getCname();
								tablebean.setEname(ename + "M");
								tablebean.setCname(cname + "(核定檔)");
								tableList.add(tablebean);
							}
							count++;
						}
					}
				}
			}
			new ListGenerator().create(bean, tableList);
			System.out.println("🈡 執行結果 " + bean.getName() + "共轉換 " + count + "個");
			System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
		}
		sc.close();
	}
	
	/**
	 * 產出表格清單。
	 */
	public void create(WorkBean bean, LinkedList<TableBean> tableList){
		try{
			LinkedList<TableBean> array = new LinkedList<TableBean>();
			String init = null;
			for(TableBean tablebean : tableList){
				if(init == null || !init.equals(tablebean.getType())){
					TableBean table = new TableBean();
					table.setNo(tablebean.getType());
					array.add(table);
					init = tablebean.getType();
				}
				array.add(tablebean);
			}
			String from = getWorkRoot() + sample;
			String to = getWorkRoot() + "✿DOCUMENT\\" + bean.getName() + topath;
			LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
			Configure config = Configure.builder().bind("table", policy).build();
			XWPFTemplate template = XWPFTemplate.compile(from, config).render(
				new HashMap<String, Object>(){{
					put("project", bean.getName().substring(3) + "表格清單");
					put("table", array);
				}}
			); 
			template.writeAndClose(new FileOutputStream(to)); 
		}catch(Exception e){
			e.printStackTrace();
		}
	}
}
