package com.ejcic.controller.emitter;

import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequestMapping("/emitter/PE01")
public class PE01 {
    
    @RequestMapping
	public void execute(@RequestParam Map<String, Object> params, Model model){
        
	}
}
