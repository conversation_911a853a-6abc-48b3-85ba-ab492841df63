package jcs.autoconfigure;

import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.jndi.JndiObjectFactoryBean;

import lombok.extern.slf4j.Slf4j;

/**
 * JndiDataSourcesAutoConfiguration
 *
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * 
 * jcs.datasource.jndi.tw=jdbc/ELTW
 * jcs.datasource.jndi.us=jdbc/NJCICUSDB
 * 
 * 
 * 
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass(JndiObjectFactoryBean.class)
@EnableConfigurationProperties(DataSourceJndiProperties.class)
public class JndiDataSourcesAutoConfiguration {

    /**
     * 
     *
     * @param beanFactory
     * @param props
     * @return InitializingBean
     */
    @Bean
    public InitializingBean jndiDataSourcesRegistrar(ConfigurableListableBeanFactory beanFactory,
            DataSourceJndiProperties props) {
        return () -> {
            Map<String, String> map = props.getJndi();
            if (map == null || map.isEmpty()) {
                log.debug("No jcs.datasource.jndi entries found; skip JNDI DataSource auto-registration");
                return;
            }
            for (Map.Entry<String, String> e : map.entrySet()) {
                String beanName = e.getKey();
                String jndiName = e.getValue();
                if (beanFactory.containsBean(beanName)) {
                    log.debug("Skip JNDI DataSource '{}' -> {} (bean already exists)", beanName, jndiName);
                    continue;
                }
                try {
                    JndiObjectFactoryBean jndi = new JndiObjectFactoryBean();
                    jndi.setJndiName(jndiName);
                    jndi.setProxyInterface(DataSource.class);
                    jndi.afterPropertiesSet();
                    DataSource ds = (DataSource) jndi.getObject();
                    beanFactory.registerSingleton(beanName, @NonNull ds);
                    log.info("Registered JNDI DataSource bean '{}' -> {}", beanName, jndiName);
                } catch (Exception ex) {
                    log.warn("Failed to register JNDI DataSource bean '{}' -> {}: {}", beanName, jndiName,
                            ex.getMessage());
                }
            }
        };
    }
}
