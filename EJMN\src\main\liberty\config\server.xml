<?xml version="1.0" encoding="UTF-8"?>
<server description="EJMN SERVER">

    <!-- Enable features -->
    <featureManager>
        <feature>servlet-6.0</feature>
        <feature>pages-3.1</feature>
        <feature>springBoot-3.0</feature>
        <feature>jdbc-4.3</feature>
        <feature>jndi-1.0</feature>
        <feature>ssl-1.0</feature>
        <feature>transportSecurity-1.0</feature>
    </featureManager>

    <!-- Database Libraries -->
    <library id="DB2Lib">
        <fileset dir="${server.config.dir}/lib" includes="db2jcc4.jar" />
    </library>
    
    <!-- JDBC Drivers -->
    <jdbcDriver id="db2Driver" libraryRef="DB2Lib" />

    <!-- DataSource for TW -->
    <dataSource id="eltw" jndiName="jdbc/ELTW">
        <jdbcDriver libraryRef="DB2Lib"/>
        <properties.db2.jcc databaseName="${db.tw.databaseName}" 
            serverName="${db.tw.serverName}" portNumber="${db.tw.portNumber}" 
            user="${db.tw.user}" password="${db.tw.password}" currentSchema="${db.tw.currentSchema}" 
            progressiveStreaming="${db.tw.progressiveStreaming}"/>
    </dataSource>

    <webApplication id="EJMN" location="EJMN.war" contextRoot="/EJMN" />

    <!-- Default SSL configuration enables trust for default certificates from the Java runtime -->
    <keyStore id="defaultKeyStore" location="key.jks" password="changeit" />
    <ssl id="defaultSSLConfig" keyStoreRef="defaultKeyStore" />

    <!-- To access this server from a remote client add a host attribute to the following element,
    e.g. host="*" -->
    <httpEndpoint id="defaultHttpEndpoint"
        host="*"    
        httpPort="9082"
        httpsPort="9445"
        sslRef="defaultSSLConfig" />

    <!-- Automatically expand WAR files and EAR files -->
    <applicationManager autoExpand="true" />

</server>