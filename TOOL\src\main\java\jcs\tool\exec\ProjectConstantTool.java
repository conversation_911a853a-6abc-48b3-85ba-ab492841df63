package jcs.tool.exec;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.CharBuffer;
import java.util.Calendar;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import jcs.tool.bean.WorkBean;

/**
 * 系統常數工具。
 */
public class ProjectConstantTool extends Tool{
	// public static void main(String[] args) throws Exception{
	// Scanner sc = args.length == 0 ? new Scanner(System.in) : new
	// Scanner(args[0]);
	// List<WorkBean> workList = getWork(sc, projectconstant, args);
	// for(WorkBean bean : workList){
	// ProjectConstantGenerator.main(new String[]{bean.getCode()});
	// }
	// }
	public static String selectProject = "1";
	
	public static final String us_properties = File.separator + "src" + File.separator + "main" +
			File.separator + "resources" + File.separator + "locale" + File.separator + "ProjectConstant_en_US.properties";
	public static final String tw_properties = File.separator + "src" + File.separator + "main" +
			File.separator + "resources" + File.separator + "locale" + File.separator + "ProjectConstant_zh_TW.properties";
	
	
	public static void main(String[] args){
		// 創建 JFrame 實例
		JFrame frame = new JFrame("執行系統常數轉換");
		init(frame); // 初始化
		// Setting the width and height of frame
		frame.setSize(600, 250);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		frame.setLocationRelativeTo(null); // 置中
		frame.setResizable(false); // 不可變動大小
		/*
		 * 創建面板，這個類似於 HTML 的 div 標簽 我們可以創建多個面板並在 JFrame 中指定位置
		 * 面板中我們可以添加文本字段，按鈕及其他組件。
		 */
		JPanel panel = new JPanel();
		// 添加面板
		frame.add(panel);
		/*
		 * 調用用戶定義的方法並添加組件到面板
		 */
		placeComponents(panel);
		// 設置界面可見
		frame.setVisible(true); // 設置界面可見
	}
	
	private static void placeComponents(JPanel panel){
		panel.setLayout(null); // 設置布局為 null
		// 創建 JLabel
		JLabel radioLabel = new JLabel("請選擇系統：");
		/*
		 * 這個方法定義了組件的位置。 setBounds(x, y, width, height) x 和 y 指定左上角的新位置，由 width
		 * 和 height 指定新的大小。
		 */
		radioLabel.setFont(defFont);
		radioLabel.setBounds(10, 0, 200, 30);
		panel.add(radioLabel);
		ButtonGroup radioGroup = new ButtonGroup();
		int r = 3; // 3個選項為1行
		for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
			Path path = field.getAnnotation(Path.class);
			int v = new BigDecimal(path.exec()).intValue();
			int w = v / r;
			if(v % r == 0)
				w--;
			int x = (v - (w * r + 1)) * 200 + 10, y = 30 * (w + 1);
			JRadioButton radio = new JRadioButton(field.getName(), v == 1);
			radio.setFont(defFont);
			radio.setBounds(x, y, 200, 30);
			radio.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e){
					selectProject = path.exec();
				}
			});
			panel.add(radio);
			radioGroup.add(radio);
		}
		// xls2java按鈕
		JLabel buttonLabel = new JLabel("請選擇轉換功能：");
		buttonLabel.setFont(defFont);
		buttonLabel.setBounds(10, 120, 200, 30);
		panel.add(buttonLabel);
		JButton xls2java = new JButton("XLS => JAVA");
		xls2java.setFont(defFont);
		xls2java.setBounds(10, 150, 200, 30);
		xls2java.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent event){
				processing(true);
				Thread t = new Thread(new Runnable() {
					public void run(){
						try{
							xlsxToJava(selectProject);
							xlsxToI18n(selectProject, us_properties, true);
							xlsxToI18n(selectProject, tw_properties, false);
							removeTimeFlag(selectProject, us_properties);
							removeTimeFlag(selectProject, tw_properties);
							alert("執行完成！");
							System.exit(0);
						}catch(Exception e){
							alert("執行失敗！");
						}finally{
							processing(false);
						}
					}
				});
				t.start();
			}
		});
		panel.add(xls2java);
		// java2xls按鈕
		JButton java2xls = new JButton("JAVA => XLS");
		java2xls.setFont(defFont);
		java2xls.setBounds(170, 150, 200, 25);
		java2xls.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent event){
				processing(true);
				Thread t = new Thread(new Runnable() {
					public void run(){
						try{
							// ProjectConstantTool.javaToXls(selectProject);
							alert("執行完成！");
						}catch(Exception e){
							alert("執行失敗！");
						}finally{
							processing(false);
						}
					}
				});
				t.start();
			}
		});
		// panel.add(java2xls);
	}
	
	/**
	 * XLSX轉換為JAVA。
	 * 
	 * @param v
	 * @throws Exception
	 */
	private static void xlsxToJava(String v) throws Exception{
		WorkBean workBean = getWorkBean(v);
		Workbook wb = null;
		InputStream in = null;
		FileWriter fw = null;
		StringBuilder path = new StringBuilder();
		StringBuilder constant = new StringBuilder();
		StringBuilder importStr = new StringBuilder("import org.springframework.beans.factory.annotation.Value;\n\n");
		boolean annotation = false;
		constant.append("package com.system.constant;").append("\n");
		constant.append("\n");
		constant.append(importStr.toString());
		constant.append("/**").append("\n");
		constant.append(" * 系統常數。").append("\n");
		constant.append(" */").append("\n");
		constant.append("public interface ProjectConstant extends EloanConstant{");
		path.append(Tool.getWorkRoot()).append("Tool");
		path.append(File.separator).append("src");
		path.append(File.separator).append("jcs");
		path.append(File.separator).append("tool");
		path.append(File.separator).append(workBean.getProject());
		path.append("_ProjectConstant.xlsx");
		File xlsx = new File(path.toString());
		Set<String> names = new HashSet<String>();
		try{
			in = new FileInputStream(xlsx);
			wb = WorkbookFactory.create(in);
			Sheet sheet = wb.getSheetAt(0);
			for(Row row : sheet){
				int rowIndex = row.getRowNum();
				if(rowIndex > 0){
					String name = row.getCell(0).toString();
					String key = row.getCell(1).toString();
					String value = row.getCell(2).toString();
					String display = row.getCell(3) == null ? null : row.getCell(3).toString();
					if(!name.isEmpty()){
						if(!names.contains(name)){
							if(names.size() > 0){
								constant.append("	}").append("\n");
							}else{
								constant.append("\n");
							}
							constant.append("\n");
							constant.append("	/** ").append(key).append(" **/").append("\n");
							constant.append("	interface ").append(name).append(" {");
							constant.append("\n");
							names.add(name);
						}else{
							if(display != null && display.trim().length() > 0){
								constant.append("		@Value(\"").append(display).append("\")");
								constant.append("String ").append(value).append(" = \"").append(key).append("\";");
								constant.append("\n");
								annotation = true;
							}else{
								constant.append("		String ").append(value).append(" = \"").append(key)
										.append("\";");
								constant.append("\n");
							}
						}
					}
				}
			}
			constant.append("	}").append("\n");
			constant.append("}");
			path.delete(0, path.length());
			path.append(Tool.getWorkRoot()).append(workBean.getProject());
			path.append(File.separator).append("src");
			path.append(File.separator).append("main");
			path.append(File.separator).append("java");
			path.append(File.separator).append("com");
			path.append(File.separator).append("system");
			path.append(File.separator).append("constant");
			path.append(File.separator).append("ProjectConstant.java");
			System.out.println(path.toString());
			File java = new File(path.toString());
			if(java.exists())
				java.delete();
			java.getParentFile().mkdirs();
			fw = new FileWriter(path.toString());
			fw.write(annotation == false ? constant.toString().replaceAll(importStr.toString(), "")
					: constant.toString());
			fw.flush();
		}catch(Exception e){
			e.printStackTrace();
			throw e;
		}finally{
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(wb != null)
					wb.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(in != null)
					in.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * XLSX轉換為I18n。
	 * 
	 * @param v
	 * @param prop
	 * @param useGoogle
	 */
	private static void xlsxToI18n(String v, String prop, boolean useGoogle){
		WorkBean workBean = getWorkBean(v);
		Workbook wb = null;
		InputStream in = null;
		FileWriter fw = null;
		OutputStream output = null;
		StringBuilder path = new StringBuilder();
		path.append(getWorkRoot()).append("Tool");
		path.append(File.separator).append("src");
		path.append(File.separator).append("jcs");
		path.append(File.separator).append("tool");
		path.append(File.separator).append(workBean.getProject());
		path.append("_ProjectConstant.xlsx");
		File xlsx = new File(path.toString());
		LinkedList<Map<String, String>> i18nList = new LinkedList<Map<String, String>>();
		try{
			in = new FileInputStream(xlsx);
			wb = WorkbookFactory.create(in);
			Sheet sheet = wb.getSheetAt(0);
			for(Row row : sheet){
				int rowIndex = row.getRowNum();
				if(rowIndex > 0){
					String name = row.getCell(0).toString();
					String value = row.getCell(2).toString();
					String display = row.getCell(3) == null ? null : row.getCell(3).toString();
					if(!value.isEmpty()){
						HashMap<String, String> map = new HashMap<String, String>();
						if(display != null && display.trim().length() > 0){
							map.put("KEY", name.trim() + "." + value.trim());
							map.put("VALUE", display.trim());
						}else{
							map.put("KEY", name.trim() + "." + value.trim());
							map.put("VALUE", value.trim());
						}
						i18nList.add(map);
					}
				}
			}
			// 載入舊Properties
			Properties old = new Properties();
			old.load(new FileReader(getWorkRoot() + workBean.getProject() + prop));
			// 建立新Properties
			Properties p = new Properties() {
				private static final long serialVersionUID = 1L;
				@Override
				public synchronized Enumeration<Object> keys(){
					return Collections.enumeration(new TreeSet<Object>(super.keySet()));
				}
			};
			System.out.println(prop);
			for(Map<String, String> map : i18nList){
				if(old.containsKey(map.get("KEY"))){
					p.setProperty(map.get("KEY"), old.getProperty(map.get("KEY")));
				}else{
					if(useGoogle){
						String translatedText = translateWord(map.get("VALUE"));
						p.setProperty(map.get("KEY"), translatedText);
						System.out.println(map.get("KEY") + " -> " + translatedText);
					}else{
						p.setProperty(map.get("KEY"), map.get("VALUE"));
					}
				}
			}
			output = new FileOutputStream(getWorkRoot() + workBean.getProject() + prop);
			p.store(output, null);
			output.flush();
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			try{
				if(output != null)
					output.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(wb != null)
					wb.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(in != null)
					in.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 移除Properties時間註記。
	 * 
	 * @param v
	 * @param prop
	 * @throws IOException
	 */
	private static void removeTimeFlag(String v, String prop){
		WorkBean workBean = getWorkBean(v);
		File propfile = new File(getWorkRoot() + workBean.getProject() + prop);
		StringBuilder str = new StringBuilder();
		CharBuffer buf = CharBuffer.allocate(1024);
		FileInputStream fis = null;
		InputStreamReader isr = null;
		BufferedReader in = null;
		FileWriter fw = null;
		try{
			fis = new FileInputStream(propfile);
			isr = new InputStreamReader(fis, "utf-8");
			in = new BufferedReader(isr);
			int year = Calendar.getInstance().get(Calendar.YEAR);
			while(in.read(buf) > 0){
				StringBuilder context = new StringBuilder();
				context.append(buf.flip());
				if(context.indexOf(" " + year) > 0){
					str.append("#ProjectConstant" + context.toString().substring(context.indexOf(" " + year) + 5));
				}else{
					str.append(context.toString());
				}
			}
			in.close();
			fw = new FileWriter(propfile);
			fw.write(str.toString());
			fw.flush();
		}catch(IOException e){
			e.printStackTrace();
		}finally{
			try{
				if(in != null)
					in.close();
				if(isr != null)
					isr.close();
				if(fis != null)
					fis.close();
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}finally{
				in = null;
				isr = null;
				fis = null;
				fw = null;
			}
		}
	}
}
