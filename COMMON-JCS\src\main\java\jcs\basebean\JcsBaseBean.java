package jcs.basebean;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import jcs.annotation.NullToSpace;
import jcs.constant.CommonConstant;
import jcs.util.StringUtil;

/**
 * JcsBaseBean。
 */
public class JcsBaseBean implements Serializable{
	private static final long serialVersionUID = 1L;
	
	/**
	 * toString
	 */
	@Override
	public String toString(){
		// return ReflectionToStringBuilder.toString(this,
		// ToStringStyle.DEFAULT_STYLE);
		return ReflectionToStringBuilder.toString(this, ToStringStyle.MULTI_LINE_STYLE);
	}
	
	/**
	 * get
	 * 
	 * @param name
	 * @return
	 */
	public String get(String name){
		if(name != null){
			try{
				// return BeanUtils.getProperty(this, name);
				// NullToSpace by fantasy 2025/07/23
				String value = BeanUtils.getProperty(this, name);
				Field field = this.getClass().getDeclaredField(name);
				if(field != null && field.isAnnotationPresent(NullToSpace.class)){
					if(StringUtil.isEmpty(value))
						value = CommonConstant.EMPTY;
				}
				return value;
			}catch(IllegalAccessException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(InvocationTargetException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(NoSuchMethodException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(NoSuchFieldException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(SecurityException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}
		}
		return null;
	}
	
	/**
	 * set
	 * 
	 * @param name
	 * @param value
	 */
	public void set(String name, Object value){
		if(name != null){
			try{
				// NullToSpace by fantasy 2025/07/23
				Field field = this.getClass().getDeclaredField(name);
				if(field != null && field.isAnnotationPresent(NullToSpace.class)){
					if(StringUtil.isEmpty(value))
						value = CommonConstant.NULL_TO_SPACE;
				}
				BeanUtils.setProperty(this, name, value);
			}catch(IllegalAccessException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(InvocationTargetException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(NoSuchFieldException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}catch(SecurityException e){
				// LOG.error(ExceptionUtils.getFullStackTrace(e));
			}
		}
	}
}
