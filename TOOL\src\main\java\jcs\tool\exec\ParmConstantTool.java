package jcs.tool.exec;

import java.util.List;
import java.util.Scanner;

import jcs.tool.bean.WorkBean;
import jcs.tool.generator.ParmConstantGenerator;

/**
 * 系統選單工具。
 */
public class ParmConstantTool extends Tool{
	
	public static void main(String[] args) throws Exception{
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, parmconstant, args);
		ParmConstantGenerator.main(new String[]{workList.size() > 0 ? "Y" : "N"});
	}
}
