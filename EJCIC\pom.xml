<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>tcb.jcic</groupId>
		<artifactId>tcb-ejcic-parent</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<groupId>tcb.jcic</groupId>
	<artifactId>EJCIC</artifactId>
	<version>${revision}</version>
	<packaging>war</packaging>
	<name>EJCIC</name>
	<description>EJCIC project for Spring Boot</description>
	<url />
	<licenses>
		<license />
	</licenses>
	<developers>
		<developer />
	</developers>
	<scm>
		<connection />
		<developerConnection />
		<tag />
		<url />
	</scm>
	<dependencies>
		<!-- Servlet API available from Liberty, compile only -->
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- App-level Spring Boot starters (moved from COMMON-JCS) -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<!-- Enable Spring AOP for @UseDataSource aspect -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>


		<!-- Common Web auto-configuration starter -->
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-WEB-STARTER</artifactId>
			<version>${project.version}</version>
		</dependency>
		<!-- Common Data auto-configuration starter (routing, JPA helpers) -->
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-DATA-STARTER</artifactId>
			<version>${project.version}</version>
		</dependency>


		<!-- Internal shared modules -->
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-JCS</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>COMMON-BANK</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>tcb.jcic</groupId>
			<artifactId>EJCIC-JPA</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

	</dependencies>

	<build>
		<finalName>EJCIC</finalName>
	</build>

	<profiles>
		<profile>
			<id>dev</id>
			<build>
				<plugins>
					<!-- Liberty Maven Plugin: only for local dev -->
					<plugin>
						<groupId>io.openliberty.tools</groupId>
						<artifactId>liberty-maven-plugin</artifactId>
						<version>3.11.5</version>
						<configuration>
							<runtimeArtifact>
								<groupId>com.ibm.websphere.appserver.runtime</groupId>
								<artifactId>wlp-webProfile10</artifactId>
								<version>********</version>
								<type>zip</type>
							</runtimeArtifact>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

</project>