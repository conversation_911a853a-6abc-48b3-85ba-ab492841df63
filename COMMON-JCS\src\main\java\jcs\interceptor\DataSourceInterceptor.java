package jcs.interceptor;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 數據源攔截器。
 */
@Component
public class DataSourceInterceptor implements HandlerInterceptor{
	
	/**
	 * 該方法將在請求處理之前調用 。
	 * 
	 * @param request HttpServletRequest
	 * @param response HttpServletResponse
	 * @param handler Object
	 * @return boolean
	 */
	@Override
	public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception{
		return true;
	}
	
	/**
	 * 當前所屬的Interceptor 的preHandle 方法的返回值=true時才能被調用。 當前請求進行處理之後，也就是Controller
	 * 方法調用之後執行， 但是它會在DispatcherServlet進行視圖返回渲染之前被調用，
	 * 所以我們可以在這個方法中對Controller處理之後的ModelAndView對象進行操作。
	 * 
	 * @param request HttpServletRequest
	 * @param response HttpServletResponse
	 * @param handler Object
	 * @param modelAndView ModelAndView
	 */
	@Override
	public void postHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler,
			@Nullable ModelAndView modelAndView) throws Exception{
	}
	
	/**
	 * 該方法將在整個請求結束之後，也就是在DispatcherServlet渲染了對應的視圖之後執行。 這個方法的主要作用是用於進行資源清理工作的。
	 * 
	 * @param request HttpServletRequest
	 * @param response HttpServletResponse
	 * @param handler Object
	 * @param exception Exception
	 */
	@Override
	public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler,
			@Nullable Exception exception) throws Exception{
	}
}
