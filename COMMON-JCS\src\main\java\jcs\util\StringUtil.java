package jcs.util;

import java.math.BigDecimal;

import jcs.constant.CommonConstant;

/**
 * 這是一個公用程式，提供String類別的處理函數。
 */
public final class StringUtil{

    /**
	 * 去除前後的空白字元。
	 *
	 * @param str
	 * @return String
	 */
	public static String trim(Object str){
		if(str == null){
			return CommonConstant.EMPTY;
		}else if(str instanceof Double){
			BigDecimal bd = BigDecimal.valueOf((Double)str);
			return bd.toPlainString();
		}else if(str instanceof Long){
			BigDecimal bd = BigDecimal.valueOf((Long)str);
			return bd.toPlainString();
		}else{
			String string = str.toString();
			char[] chars = string.toCharArray();
			int begin = 0;
			while(begin < chars.length && (chars[begin] <= ' ' || chars[begin] == '　')){
				++begin;
			}
			int end = chars.length - 1;
			while(end > begin && (chars[end] <= ' ' || chars[end] == '　')){
				--end;
			}
			if(begin == 0 && end == chars.length - 1){
				return string;
			}
			return new String(chars, begin, end - begin + 1);
		}
	}
    
    /**
	 * 是否為空值。
	 *
	 * @param obj
	 * @return boolean
	 */
	public static boolean isEmpty(Object obj){
		return isEmpty(trim(obj));
	}

    /**
	 * 是否為空值。
	 *
	 * @param str
	 * @return boolean
	 */
	public static boolean isEmpty(String str){
		return(null == str || str.trim().length() == 0);
	}

    /**
	 * 是否不為空值。
	 *
	 * @param obj
	 * @return boolean
	 */
	public static boolean isNotEmpty(Object obj){
		return !isEmpty(obj);
	}
	
	/**
	 * 是否不為空值。
	 *
	 * @param str
	 * @return boolean
	 */
	public static boolean isNotEmpty(String str){
		return !isEmpty(str);
	}


	
}
