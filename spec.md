# 精細化共用組態 Starter 規格（Plan 1）

## 目標
將所有專案都共用的 Web/Boot 組態，以 Spring Boot 3 自動組態（AutoConfiguration）形式包裝為精細化 Starter，供各 WAR 應用按需引入，並保留應用覆寫彈性。

## 模組切分
- COMMON-CORE：純工具（不含 Boot/容器耦合）
- COMMON-WEB-STARTER：共用 Web MVC／View（Freemarker、SiteMesh 等）自動組態
- COMMON-DATA-STARTER：共用 Spring Data（JPA/JDBC）自動組態

未來可擴充：COMMON-SECURITY-STARTER（如有共用 Security 組態需求）

## 自動組態機制
- 使用 Spring Boot 3 的 `@AutoConfiguration` 與 `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`
- 以條件註解控制啟用/關閉：
  - `@ConditionalOnWebApplication`、`@ConditionalOnClass`、`@ConditionalOnMissingBean`、`@ConditionalOnProperty`
- Starter 不內嵌容器，相依 `spring-boot-starter-web` 時排除 `spring-boot-starter-tomcat`；Servlet API 由 WAR 容器（Liberty）提供 `provided`

## 目前落地內容（本次提交）
- 新增三個模組並加入根 POM modules：
  - COMMON-CORE（已承載核心工具：FieldUtil / SpringUtil；後續持續遷移 commons-* 等純工具）
  - COMMON-WEB-STARTER：提供 `CommonWebAutoConfiguration` 與 imports 註冊檔；已移除 COMMON-JCS 的 WebConfig，由 Starter 接手
  - COMMON-DATA-STARTER：提供 `DataAutoConfiguration` 與 imports 註冊檔；已遷移 `jcs.repository.CustomJpaDao`
- 調整 *-JPA 模組（EJCIC-JPA/EJMN-JPA/ETCH-JPA）加入對 COMMON-DATA-STARTER 的依賴
- 已完成：將 `SpringUtil/FieldUtil` 移至 COMMON-CORE；COMMON-DATA-STARTER 現依賴 COMMON-CORE（移除對 COMMON-JCS 的暫時依賴）

- 已移除 COMMON-JCS 內 `jcs.datasource.DataSourceHolder` / `RoutingDataSource`，統一由 COMMON-DATA-STARTER 提供

## 後續遷移路線
1. 從 COMMON-JCS 複製 Web/Data 組態到對應 Starter，加入條件註解與可覆寫設計
2. 各 WAR 模組（EJCIC/EJMN/ETCH）加上對 Starter 的依賴，與 COMMON-JCS 並存驗證
3. 行為驗證一致後，從 COMMON-JCS 移除對應的 Boot/Web 依賴與組態類別
4. COMMON-JCS 轉型為 COMMON-CORE（或移動純工具到 COMMON-CORE）

## UML（Mermaid）

### 模組關係圖（物件關聯）
```mermaid
classDiagram
  class COMMON_CORE
  class COMMON_WEB_STARTER {
    +CommonWebAutoConfiguration
  }
  class COMMON_DATA_STARTER {
    +DataAutoConfiguration
  }
  class COMMON_JCS
  class EJCIC
  class EJMN
  class ETCH

  COMMON_WEB_STARTER --> EJCIC
  COMMON_WEB_STARTER --> EJMN
  COMMON_WEB_STARTER --> ETCH
  COMMON_DATA_STARTER --> EJCIC
  COMMON_DATA_STARTER --> EJMN
  COMMON_DATA_STARTER --> ETCH
  COMMON_DATA_STARTER --> COMMON_CORE : "utils (FieldUtil/SpringUtil)"
  COMMON_DATA_STARTER ..> AbstractRoutingDataSource : "DataRoutingAutoConfiguration\n(jcs.datasource.routing.enabled)"
```

### Routing DataSource 自動組態
- 啟用條件：
  - `@ConditionalOnClass(AbstractRoutingDataSource.class)`
  - `@ConditionalOnProperty(prefix="jcs.datasource.routing", name="enabled", havingValue="true")`
  - `@ConditionalOnMissingBean(name="routingDataSource")`
- 行為：
  - 聚合所有現有的 `DataSource` Bean（排除自身名稱 `routingDataSource`）為 `targetDataSources`
  - 以第一個可用的 `DataSource` 作為 `defaultTargetDataSource`
  - 以 ThreadLocal（`DataSourceHolder`）決定路由鍵，建立匿名 `AbstractRoutingDataSource` 實例

```mermaid
flowchart TD
  A[設定 jcs.datasource.routing.enabled=true] --> B{可用 DataSource 數量 > 1?}
  B -- 否 --> X[不建立 routingDataSource]
  B -- 是 --> C[聚合 targetDataSources]
  C --> D[default = 第一個 DataSource]
  D --> E[依 DataSourceHolder.lookupKey 路由]
```

### 日誌策略（Lombok）
- 於共用程式及自動組態類採用 `@lombok.extern.slf4j.Slf4j` 生成 `log` 欄位
- 編譯器：JDK 21 下無需額外 annotationProcessor 設定；JDK 23 / module 模式需在 `maven-compiler-plugin` 宣告 `annotationProcessorPaths`
- 全專案統一：所有 Controller/Security/Interceptor/AutoConfiguration 類改用 @Slf4j，並將各模組 POM 加入 lombok 依賴（scope=provided）
- 模組覆蓋：COMMON-JCS、COMMON-WEB-STARTER、EJCIC、EJMN、ETCH
- 相容性：JDK 21 無需 annotationProcessorPaths；JDK 23 或使用 module-info.java 時需於 maven-compiler-plugin 指定 lombok 為 annotationProcessorPaths
- 風險與緩解：IDE 未辨識 @Slf4j 時，重新載入 Maven 專案並確認模組 POM 已加入 provided 依賴


  COMMON_JCS ..> COMMON_CORE : "逐步遷移"
```

### 啟動序列圖（AutoConfiguration 匯入）
```mermaid
sequenceDiagram
  participant App as WAR 應用
  participant Boot as Spring Boot
  participant WebS as COMMON-WEB-STARTER
  participant DataS as COMMON-DATA-STARTER

  App->>Boot: 啟動 SpringApplication
  Boot->>Boot: 掃描 AutoConfiguration.imports
  Boot->>WebS: 匯入 CommonWebAutoConfiguration（符合條件）
  Boot->>DataS: 匯入 DataAutoConfiguration（符合條件）
  WebS-->>Boot: 註冊 Web 相關預設 Bean（可被覆寫）
  DataS-->>Boot: 註冊 Data 相關預設 Bean（可被覆寫）
  Boot-->>App: 應用啟動完成
```

### 開關流程（條件註解）
```mermaid
flowchart TD
  A[啟動] --> B{符合 Web 環境?}
  B -- 是 --> C[載入 Web AutoConfig]
  B -- 否 --> D[略過 Web AutoConfig]
  C --> E{存在應用自定義 Bean?}
  E -- 是 --> F[略過預設 Bean]
  E -- 否 --> G[註冊預設 Bean]
```

