package jcs.util;

import org.springframework.context.ApplicationContext;
import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.web.context.ContextLoader;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

/**
 * 這是一個公用程式，統一存取Spring的ApplicationContext。
 */
@SuppressWarnings("unchecked")
public abstract class SpringUtil{

    private static ApplicationContext appContext;

    /**
     * 設定目前使用的Application Context
     */
    public static void setApplicationContext(ApplicationContext context){
        appContext = context;
    }
    
    /**
     * 取得Spring的ApplicationContext
     * 
     * @return ApplicationContext
     */
    public static ApplicationContext getApplicationContext(){
        if(appContext == null){
            appContext = ContextLoader.getCurrentWebApplicationContext();
        }
        if(appContext == null){
            throw new RuntimeException("Spring not loaded.");
        }
        return appContext;
    }
    
    /**
     * 取得共享的 EntityManager（委派給 Spring 的 SharedEntityManagerCreator）。
     * @return EntityManager
     */
    public static EntityManager getEntityManager(){
        EntityManagerFactory emf = get(EntityManagerFactory.class);
        if(emf != null){
            return SharedEntityManagerCreator.createSharedEntityManager(emf);
        }
        throw new RuntimeException("JPA not loaded.");
    }

    /**
     * 取得Spring管理的Bean
     * 
     * @param name beanName
     * @param type beanType
     * @return managed bean
     */
    public static <T> T get(String name, Class<T> type){
        return getApplicationContext().getBean(name, type);
    }
    
    /**
     * 取得Spring管理的Bean
     * 
     * @param name beanName
     * @return managed bean
     */
    public static <T> T get(String name){
        return (T)getApplicationContext().getBean(name);
    }
    
    /**
     * 取得Spring管理的Bean
     * 
     * @param type Class<T>
     * @return managed bean
     */
    public static <T> T get(Class<T> type){
        ApplicationContext ctx = getApplicationContext();
        String[] names = ctx.getBeanNamesForType(type);
        if(names.length > 0){
            return (T)ctx.getBean(names[0]);
        }
        return null;
    }
}

