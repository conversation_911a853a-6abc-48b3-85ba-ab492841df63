package jcs.tool.generator;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.Scanner;

import org.apache.commons.lang3.StringUtils;

import jcs.tool.exec.Tool;
import jcs.tool.bean.ColumBean;
import jcs.tool.bean.IndexBean;
import jcs.tool.bean.TableBean;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成DDL。
 */
public class SqlGenerator extends Tool{
	// schema
	private static final String schema = "ELN";
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableupdate, args);
		for(WorkBean bean : workList){
			Integer count = 0;
			System.out.println("\n");
			System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
			System.out.println("🈠 開始執行 " + bean.getName() + "『SqlGenerator』轉換");
			String from = getWorkRoot() + "DOC\\" + bean.getName() + "\\1、資料庫設計\\1.3、表格內容說明\\"; // Doc檔案來源
			String to = getWorkRoot() + "DOC\\" + bean.getName() + "\\2、附件DDL\\"; // Sql產出至...
			String name = "create_" + bean.getProject().toLowerCase(Locale.getDefault()) + "_table.ddl";
			FileWriter fw = null;
			try{
				fw = new FileWriter(to + name);
				File folder = new File(from);
				File[] files = folder.listFiles();
				if(files != null){
					for(File file : files){
						if(file.isDirectory()){
							if(StringUtils.endsWith(file.getName(), "視圖")){
								// view folder skip
								continue;
							}
							File[] files2 = file.listFiles();
							if(files2 != null){
								for(File file2 : files2){
									if(Tool.isCreate(file2)){
										TableBean tablebean = new TableBean().parse(
												from + file.getName() + "\\" + file2.getName(), to + name,
												TableBean.Sql, file.getName());
										new SqlGenerator().create(tablebean, fw, true);
										if ("True".equals(tablebean.getHistory())) {
											String ename = tablebean.getEname();
											String cname = tablebean.getCname();
											tablebean.setEname("H" + ename);
											tablebean.setCname(cname + "(歷史)");
											new SqlGenerator().create(tablebean, fw, true);
											tablebean.setEname("V" + ename);
											tablebean.setCname(cname + "(視圖)");
											new SqlGenerator().create(tablebean, fw, false);
										} else if("Approved".equals(tablebean.getHistory())){
											String ename = tablebean.getEname();
											String cname = tablebean.getCname();
											tablebean.setEname(ename + "M");
											tablebean.setCname(cname + "(核定檔)");
											new SqlGenerator().create(tablebean, fw, true);
										}
										count++;
									}
								}
							}
						}else{
							if(Tool.isCreate(file)){
								TableBean tablebean = new TableBean().parse(from + file.getName(), to + name,
										TableBean.Sql, file.getName());
								new SqlGenerator().create(tablebean, fw, true);
								if ("True".equals(tablebean.getHistory())) {
									String ename = tablebean.getEname();
									String cname = tablebean.getCname();
									tablebean.setEname("H" + ename);
									tablebean.setCname(cname + "(歷史)");
									new SqlGenerator().create(tablebean, fw, true);
									tablebean.setEname("V" + ename);
									tablebean.setCname(cname + "(視圖)");
									new SqlGenerator().create(tablebean, fw, false);
								} else if("Approved".equals(tablebean.getHistory())){
									String ename = tablebean.getEname();
									String cname = tablebean.getCname();
									tablebean.setEname(ename + "M");
									tablebean.setCname(cname + "(核定檔)");
									new SqlGenerator().create(tablebean, fw, true);
								}
								count++;
							}
						}
					}
				}
				fw.flush();
				System.out.println("● 產製成功 ●");
			}catch(Exception e){
				e.printStackTrace();
			}finally{
				try{
					if(fw != null)
						fw.close();
				}catch(IOException e){
					e.printStackTrace();
				}
			}
			System.out.println("🈡 執行結果 " + bean.getName() + "共轉換 " + count + "個");
			System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
		}
		sc.close();
	}
	
	public void create(TableBean tablebean, FileWriter fw, boolean isTable) throws IOException {
		if (isTable) {// Vector：修正V開頭表格會造成問題
			// if(tablebean.getEname().indexOf("V") != 0){
			StringBuffer sb = new StringBuffer();
			fw.write("--------------------------------------------------\n");
			fw.write("-- " + tablebean.getCname() + "\n");
			fw.write("--------------------------------------------------\n");
			// drop
			fw.write("DROP TABLE " + schema + "." + tablebean.getEname() + ";\n");
			// create
			fw.write("CREATE TABLE " + schema + "." + tablebean.getEname() + " (\n");
			for(int i = 0; i < tablebean.getColumlist().size(); i++){
				ColumBean columBean = tablebean.getColumlist().get(i);
				if(columBean.getEname().length() < 20){
					while(columBean.getEname().length() < 20){
						columBean.setEname(columBean.getEname().toUpperCase(Locale.getDefault()) + " ");
					}
				}else{// 英文欄位名稱大於20時，補一個空白，跟TYPE隔開
					columBean.setEname(columBean.getEname().toUpperCase(Locale.getDefault()) + " ");
				}
				fw.write("	" + columBean.getEname() + columBean.getType());
				if("Y".equals(columBean.getKey())){
					fw.write(" NOT NULL");
					if(sb.length() != 0){
						sb.append(", ");
					}
					sb.append(columBean.getEname().trim());
				}
				if(i + 1 == tablebean.getColumlist().size() && tablebean.getKeyColumlist().size() == 0){
					fw.write("\n");
				}else{
					fw.write(",\n");
				}
			}
			if(tablebean.getKeyColumlist().size() > 0){
				fw.write("	PRIMARY KEY(" + sb.toString() + ")\n");
			}
			fw.write(") IN " + tablebean.getTablespace() + " INDEX IN IDX_TS4K LONG IN LOB_TS4K" + "\n");
			fw.write(";\n");
		}else{
			fw.write("--------------------------------------------------\n");
			fw.write("-- " + tablebean.getCname() + "\n");
			fw.write("--------------------------------------------------\n");
			fw.write("CREATE OR REPLACE VIEW " + schema + "." + tablebean.getEname() + " AS (\n");
			fw.write("	SELECT * FROM " + schema + "." + tablebean.getEname().substring(1) + "\n");
			fw.write("	UNION ALL\n");
			fw.write("	SELECT * FROM " + schema + ".H" + tablebean.getEname().substring(1) + "\n");
			fw.write(");\n");
		}
		// comment
		fw.write("COMMENT ON " + schema + "." + tablebean.getEname() + "(\n");
		for(int i = 0; i < tablebean.getColumlist().size(); i++){
			ColumBean columBean = tablebean.getColumlist().get(i);
			fw.write("	" + columBean.getEname() + " IS '" + columBean.getCname() + "'");
			if(i + 1 < tablebean.getColumlist().size()){
				fw.write(",\n");
			}else{
				fw.write("\n");
			}
		}
		fw.write(");\n");
		// index
		if(tablebean.getEname().indexOf("H") == 0){
			for(int i = 0; i < tablebean.getIndexlist().size(); i++){
				IndexBean indexBean = tablebean.getIndexlist().get(i);
				fw.write("CREATE INDEX " + schema + ".XH" + indexBean.getName().substring(1) + " ON " + schema + "."
						+ tablebean.getEname() + " (" + indexBean.getColum() + ");\n");
			}
			fw.write("\n");
		}else if(tablebean.getEname().indexOf("V") != 0){
			for(int i = 0; i < tablebean.getIndexlist().size(); i++){
				IndexBean indexBean = tablebean.getIndexlist().get(i);
				fw.write("CREATE INDEX " + schema + "." + indexBean.getName() + " ON " + schema + "."
						+ tablebean.getEname() + " (" + indexBean.getColum() + ");\n");
			}
			fw.write("\n");
		}
	}
}
