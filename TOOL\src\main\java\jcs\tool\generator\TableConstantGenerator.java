package jcs.tool.generator;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Scanner;

import org.apache.commons.lang3.StringUtils;

import jcs.tool.exec.Tool;
import jcs.tool.bean.TableBean;
import jcs.tool.bean.WorkBean;

/**
 * 自動生成表格常數。
 */
public class TableConstantGenerator extends Tool{
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableconstant, args);
		new TableConstantGenerator().create(workList);
	}
	
	/** 產出表格常數 */
	public void create(List<WorkBean> workList){
		FileWriter fw = null;
		try{
			System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
			System.out.println("🈠 開始執行 『TableConstantGenerator』轉換");
			StringBuilder layout = new StringBuilder();
			for(WorkBean bean : workList){
				if(StringUtils.equals(bean.getCode(), "9")){
					System.out.println("批次：SKIP~~");
					continue;
				}
				String from = getWorkRoot() + "✿DOCUMENT\\2、Data Definition Language\\" + bean.getName() + "\\"; // Doc檔案來源
				File folder = new File(from);
				File[] files = folder.listFiles();
				if(files != null){
					for(File file : files){
						if(file.isDirectory()){
							File[] files2 = file.listFiles();
							if(files2 != null){
								for(File file2 : files2){
									layout = checkThenWrite(file2, bean, layout);
								}
							}
						}else{
							layout = checkThenWrite(file, bean, layout);
						}
					}
				}
			}
			StringBuilder temp = new StringBuilder();
			temp.append("package com.system.constant;").append("\n");
			temp.append("\n");
			temp.append("/**").append("\n");
			temp.append(" * 表格常數。").append("\n");
			temp.append(" */").append("\n");
			temp.append("public interface TableConstant{").append("\n");
			temp.append(layout.toString());
			temp.append("}");
			StringBuilder path = new StringBuilder();
			path.append(getWorkRoot()).append("ELOAN-COMMON");
			path.append(File.separator).append("src");
			path.append(File.separator).append("main");
			path.append(File.separator).append("java");
			path.append(File.separator).append("com");
			path.append(File.separator).append("system");
			path.append(File.separator).append("constant");
			path.append(File.separator).append("TableConstant.java");
			System.out.println(path.toString());
			File file = new File(path.toString());
			if(file.exists())
				file.delete();
			file.getParentFile().mkdirs();
			fw = new FileWriter(path.toString());
			fw.write(temp.toString());
			fw.flush();
			if(fw != null)
				fw.flush();		
			System.out.println("🈡 執行完成");
			System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}

	private StringBuilder checkThenWrite(File file, WorkBean bean, StringBuilder layout) {
		if (Tool.isCreate(file)) {
			TableBean tablebean = new TableBean().parse(
					file.getAbsolutePath(), null, null, file.getName());
			layout.append("	final String ");
			layout.append(bean.getProject());
			layout.append("_");
			layout.append(tablebean.getCname().replaceAll("(\\))", "").replaceAll("(\\(|\\~|\\、|\\-)", "_"));
			layout.append(" = \"");
			layout.append(tablebean.getEname());
			layout.append("\";").append("\n");
			if ("True".equals(tablebean.getHistory())) {
				System.out.println("產製 " + tablebean.getCname() + " (" + tablebean.getEname() + ")歷史檔及View");
				// HIS
				layout.append("	final String ");
				layout.append(bean.getProject());
				layout.append("_");
				layout.append(tablebean.getCname().replaceAll("(\\))", "").replaceAll("(\\(|\\~|\\、|\\-)", "_"));
				layout.append("_歷史");
				layout.append(" = \"");
				layout.append("H").append(tablebean.getEname());
				layout.append("\";").append("\n");
				// VIEW
				layout.append("	final String ");
				layout.append(bean.getProject());
				layout.append("_");
				layout.append(tablebean.getCname().replaceAll("(\\))", "").replaceAll("(\\(|\\~|\\、|\\-)", "_"));
				layout.append("_視圖");
				layout.append(" = \"");
				layout.append("V").append(tablebean.getEname());
				layout.append("\";").append("\n");
			} else if ("Approved".equals(tablebean.getHistory())) {
				System.out.println("產製 " + tablebean.getCname() + " (" + tablebean.getEname() + ")核定檔");
				// HIS
				layout.append("	final String ");
				layout.append(bean.getProject());
				layout.append("_");
				layout.append(tablebean.getCname().replaceAll("(\\))", "").replaceAll("(\\(|\\~|\\、|\\-)", "_"));
				layout.append("_核定");
				layout.append(" = \"");
				layout.append(tablebean.getEname()).append("M");
				layout.append("\";").append("\n");
			}
		}
		return layout;
	}
}
