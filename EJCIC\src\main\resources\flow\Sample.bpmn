<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:bpsim="http://www.bpsim.org/schemas/1.0" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:drools="http://www.jboss.org/drools" id="_RPvt8FHxED6nwbQNPNMzsw" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd http://www.jboss.org/drools drools.xsd http://www.bpsim.org/schemas/1.0 bpsim.xsd http://www.omg.org/spec/DD/20100524/DC DC.xsd http://www.omg.org/spec/DD/20100524/DI DI.xsd " exporter="jBPM Process Modeler" exporterVersion="2.0" targetNamespace="http://www.omg.org/bpmn20">
  <bpmn2:itemDefinition id="_resultItem" structureRef="String"/>
  <bpmn2:itemDefinition id="userTask1_InMessageType" structureRef=""/>
  <bpmn2:itemDefinition id="userTask1_OutMessageType" structureRef=""/>
  <bpmn2:itemDefinition id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_InMessageType" structureRef=""/>
  <bpmn2:itemDefinition id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_OutMessageType" structureRef=""/>
  <bpmn2:message id="userTask1_InMessage" itemRef="userTask1_InMessageType"/>
  <bpmn2:message id="userTask1_OutMessage" itemRef="userTask1_OutMessageType"/>
  <bpmn2:interface id="userTask1_ServiceInterface" name="" implementationRef="">
    <bpmn2:operation id="userTask1_ServiceOperation" name="" implementationRef="">
      <bpmn2:inMessageRef>userTask1_InMessage</bpmn2:inMessageRef>
      <bpmn2:outMessageRef>userTask1_OutMessage</bpmn2:outMessageRef>
    </bpmn2:operation>
  </bpmn2:interface>
  <bpmn2:message id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_InMessage" itemRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7_InMessageType"/>
  <bpmn2:message id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_OutMessage" itemRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7_OutMessageType"/>
  <bpmn2:interface id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_ServiceInterface" name="" implementationRef="">
    <bpmn2:operation id="_33501EF9-9B75-4570-B55D-F3AE39E564B7_ServiceOperation" name="" implementationRef="">
      <bpmn2:inMessageRef>_33501EF9-9B75-4570-B55D-F3AE39E564B7_InMessage</bpmn2:inMessageRef>
      <bpmn2:outMessageRef>_33501EF9-9B75-4570-B55D-F3AE39E564B7_OutMessage</bpmn2:outMessageRef>
    </bpmn2:operation>
  </bpmn2:interface>
  <bpmn2:collaboration id="_6F452978-772D-4209-A1B7-C9D225E64DBD" name="Default Collaboration">
    <bpmn2:participant id="_7FA7D567-5F66-4723-9791-28DA4185526E" name="Pool Participant" processRef="Sample"/>
  </bpmn2:collaboration>
  <bpmn2:process id="Sample" drools:packageName="com.project.flow" drools:version="1.0" drools:adHoc="false" name="Sample" isExecutable="true" processType="Public">
    <bpmn2:property id="result" itemSubjectRef="_resultItem" name="result"/>
    <bpmn2:sequenceFlow id="_A6862188-550A-4359-94F9-BDA196CB3745" name="退回經辦" sourceRef="_676AF165-EB35-495D-A654-683A62AED041" targetRef="_C8C4ADD7-5F44-40CC-B257-6099BE7EB306">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[退回經辦]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return "N".equals(result);]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_9A846EF6-EDAA-40B7-8217-BF988BA973FC" name="主管覆核" sourceRef="_676AF165-EB35-495D-A654-683A62AED041" targetRef="_3655F3F8-CF7E-4679-959E-322800BB30D5">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[主管覆核]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return "Y".equals(result);]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_3148F552-9ECE-4C98-B0A3-1E104EA38D6A" sourceRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7" targetRef="_676AF165-EB35-495D-A654-683A62AED041"/>
    <bpmn2:sequenceFlow id="_4B065C51-AF4F-4C92-BF90-E76E2D388C41" name="完成送出" sourceRef="_76DD5B2C-76EC-4D77-96A9-C30119D27B42" targetRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[完成送出]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return "Y".equals(result);]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_2A93EF29-61F3-4EEC-A2E4-C594B29692E7" name="案件刪除" sourceRef="_76DD5B2C-76EC-4D77-96A9-C30119D27B42" targetRef="_5574D20E-B647-48AD-A144-9BE76350ADAF">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[案件刪除]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return "N".equals(result);]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:sequenceFlow id="_5039B813-2C81-47F0-BBF0-9F23E7B26BE2" sourceRef="start" targetRef="_C8C4ADD7-5F44-40CC-B257-6099BE7EB306"/>
    <bpmn2:sequenceFlow id="_A607533D-2FB5-4533-B4B2-0C0846FADCA4" sourceRef="userTask1" targetRef="_76DD5B2C-76EC-4D77-96A9-C30119D27B42"/>
    <bpmn2:sequenceFlow id="_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9" sourceRef="_C8C4ADD7-5F44-40CC-B257-6099BE7EB306" targetRef="userTask1">
      <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression" language="http://www.java.com/java"><![CDATA[return "Y".equals(result);]]></bpmn2:conditionExpression>
    </bpmn2:sequenceFlow>
    <bpmn2:exclusiveGateway id="_C8C4ADD7-5F44-40CC-B257-6099BE7EB306" drools:dg="_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9" gatewayDirection="Converging" default="_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9">
      <bpmn2:incoming>_5039B813-2C81-47F0-BBF0-9F23E7B26BE2</bpmn2:incoming>
      <bpmn2:incoming>_A6862188-550A-4359-94F9-BDA196CB3745</bpmn2:incoming>
      <bpmn2:outgoing>_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:endEvent id="_3655F3F8-CF7E-4679-959E-322800BB30D5" name="End">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[End]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_9A846EF6-EDAA-40B7-8217-BF988BA973FC</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:exclusiveGateway id="_676AF165-EB35-495D-A654-683A62AED041" gatewayDirection="Diverging">
      <bpmn2:incoming>_3148F552-9ECE-4C98-B0A3-1E104EA38D6A</bpmn2:incoming>
      <bpmn2:outgoing>_9A846EF6-EDAA-40B7-8217-BF988BA973FC</bpmn2:outgoing>
      <bpmn2:outgoing>_A6862188-550A-4359-94F9-BDA196CB3745</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:serviceTask id="_33501EF9-9B75-4570-B55D-F3AE39E564B7" drools:serviceimplementation="Java" drools:serviceinterface="" drools:serviceoperation="" name="覆核中" implementation="Java" operationRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7_ServiceOperation">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[覆核中]]></drools:metaValue>
        </drools:metaData>
        <drools:metaData name="role">
          <drools:metaValue><![CDATA[B02]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_4B065C51-AF4F-4C92-BF90-E76E2D388C41</bpmn2:incoming>
      <bpmn2:outgoing>_3148F552-9ECE-4C98-B0A3-1E104EA38D6A</bpmn2:outgoing>
      <bpmn2:ioSpecification/>
    </bpmn2:serviceTask>
    <bpmn2:exclusiveGateway id="_76DD5B2C-76EC-4D77-96A9-C30119D27B42" gatewayDirection="Diverging">
      <bpmn2:incoming>_A607533D-2FB5-4533-B4B2-0C0846FADCA4</bpmn2:incoming>
      <bpmn2:outgoing>_2A93EF29-61F3-4EEC-A2E4-C594B29692E7</bpmn2:outgoing>
      <bpmn2:outgoing>_4B065C51-AF4F-4C92-BF90-E76E2D388C41</bpmn2:outgoing>
    </bpmn2:exclusiveGateway>
    <bpmn2:endEvent id="_5574D20E-B647-48AD-A144-9BE76350ADAF" name="End">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[End]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_2A93EF29-61F3-4EEC-A2E4-C594B29692E7</bpmn2:incoming>
    </bpmn2:endEvent>
    <bpmn2:startEvent id="start" name="Start">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[Start]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:outgoing>_5039B813-2C81-47F0-BBF0-9F23E7B26BE2</bpmn2:outgoing>
    </bpmn2:startEvent>
    <bpmn2:serviceTask id="userTask1" drools:serviceimplementation="Java" drools:serviceinterface="" drools:serviceoperation="" name="編製中" implementation="Java" operationRef="userTask1_ServiceOperation">
      <bpmn2:extensionElements>
        <drools:metaData name="elementname">
          <drools:metaValue><![CDATA[編製中]]></drools:metaValue>
        </drools:metaData>
        <drools:metaData name="role">
          <drools:metaValue><![CDATA[B01]]></drools:metaValue>
        </drools:metaData>
      </bpmn2:extensionElements>
      <bpmn2:incoming>_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9</bpmn2:incoming>
      <bpmn2:outgoing>_A607533D-2FB5-4533-B4B2-0C0846FADCA4</bpmn2:outgoing>
      <bpmn2:ioSpecification/>
    </bpmn2:serviceTask>
  </bpmn2:process>
  <bpmndi:BPMNDiagram>
    <bpmndi:BPMNPlane bpmnElement="Sample">
      <bpmndi:BPMNShape id="shape_userTask1" bpmnElement="userTask1">
        <dc:Bounds height="56" width="100" x="400.3333333333333" y="94.11111111111111"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape_start" bpmnElement="start">
        <dc:Bounds height="56" width="56" x="202.55555555555554" y="93.88888888888889"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__5574D20E-B647-48AD-A144-9BE76350ADAF" bpmnElement="_5574D20E-B647-48AD-A144-9BE76350ADAF">
        <dc:Bounds height="56" width="56" x="552.3333333333333" y="206"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__76DD5B2C-76EC-4D77-96A9-C30119D27B42" bpmnElement="_76DD5B2C-76EC-4D77-96A9-C30119D27B42">
        <dc:Bounds height="56" width="56" x="552.3333333333333" y="94.11111111111111"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__33501EF9-9B75-4570-B55D-F3AE39E564B7" bpmnElement="_33501EF9-9B75-4570-B55D-F3AE39E564B7">
        <dc:Bounds height="56" width="104" x="688.3333333333333" y="94.11111111111111"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__676AF165-EB35-495D-A654-683A62AED041" bpmnElement="_676AF165-EB35-495D-A654-683A62AED041">
        <dc:Bounds height="56" width="56" x="856.3333333333333" y="94.11111111111111"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__3655F3F8-CF7E-4679-959E-322800BB30D5" bpmnElement="_3655F3F8-CF7E-4679-959E-322800BB30D5">
        <dc:Bounds height="56" width="56" x="980.3333333333333" y="94.11111111111111"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape__C8C4ADD7-5F44-40CC-B257-6099BE7EB306" bpmnElement="_C8C4ADD7-5F44-40CC-B257-6099BE7EB306">
        <dc:Bounds height="56" width="56" x="301.3333333333333" y="93.88888888888889"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge_shape__C8C4ADD7-5F44-40CC-B257-6099BE7EB306_to_shape_userTask1" bpmnElement="_3D79CC95-52F9-4FED-8FE0-A8DAAB0F79A9">
        <di:waypoint x="329.3333333333333" y="121.88888888888889"/>
        <di:waypoint x="400.3333333333333" y="122.11111111111111"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_userTask1_to_shape__76DD5B2C-76EC-4D77-96A9-C30119D27B42" bpmnElement="_A607533D-2FB5-4533-B4B2-0C0846FADCA4">
        <di:waypoint x="500.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="552.3333333333333" y="122.11111111111111"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape_start_to_shape__C8C4ADD7-5F44-40CC-B257-6099BE7EB306" bpmnElement="_5039B813-2C81-47F0-BBF0-9F23E7B26BE2">
        <di:waypoint x="258.55555555555554" y="121.88888888888889"/>
        <di:waypoint x="301.3333333333333" y="121.88888888888889"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__76DD5B2C-76EC-4D77-96A9-C30119D27B42_to_shape__5574D20E-B647-48AD-A144-9BE76350ADAF" bpmnElement="_2A93EF29-61F3-4EEC-A2E4-C594B29692E7">
        <di:waypoint x="580.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="580.3333333333333" y="206"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__76DD5B2C-76EC-4D77-96A9-C30119D27B42_to_shape__33501EF9-9B75-4570-B55D-F3AE39E564B7" bpmnElement="_4B065C51-AF4F-4C92-BF90-E76E2D388C41">
        <di:waypoint x="580.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="688.3333333333333" y="122.11111111111111"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__33501EF9-9B75-4570-B55D-F3AE39E564B7_to_shape__676AF165-EB35-495D-A654-683A62AED041" bpmnElement="_3148F552-9ECE-4C98-B0A3-1E104EA38D6A">
        <di:waypoint x="740.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="856.3333333333333" y="122.11111111111111"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__676AF165-EB35-495D-A654-683A62AED041_to_shape__3655F3F8-CF7E-4679-959E-322800BB30D5" bpmnElement="_9A846EF6-EDAA-40B7-8217-BF988BA973FC">
        <di:waypoint x="912.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="980.3333333333333" y="122.11111111111111"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge_shape__676AF165-EB35-495D-A654-683A62AED041_to_shape__C8C4ADD7-5F44-40CC-B257-6099BE7EB306" bpmnElement="_A6862188-550A-4359-94F9-BDA196CB3745">
        <di:waypoint x="884.3333333333333" y="122.11111111111111"/>
        <di:waypoint x="884.3333333333333" y="9.666666666666657"/>
        <di:waypoint x="391.3333333333333" y="9.666666666666657"/>
        <di:waypoint x="329.3333333333333" y="9.666666666666657"/>
        <di:waypoint x="329.3333333333333" y="93.88888888888889"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
  <bpmn2:relationship type="BPSimData">
    <bpmn2:extensionElements>
      <bpsim:BPSimData>
        <bpsim:Scenario id="default" name="Simulationscenario">
          <bpsim:ScenarioParameters/>
          <bpsim:ElementParameters elementRef="userTask1">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="start">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
          </bpsim:ElementParameters>
          <bpsim:ElementParameters elementRef="_33501EF9-9B75-4570-B55D-F3AE39E564B7">
            <bpsim:TimeParameters>
              <bpsim:ProcessingTime>
                <bpsim:NormalDistribution mean="0" standardDeviation="0"/>
              </bpsim:ProcessingTime>
            </bpsim:TimeParameters>
            <bpsim:ResourceParameters>
              <bpsim:Availability>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Availability>
              <bpsim:Quantity>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:Quantity>
            </bpsim:ResourceParameters>
            <bpsim:CostParameters>
              <bpsim:UnitCost>
                <bpsim:FloatingParameter value="0"/>
              </bpsim:UnitCost>
            </bpsim:CostParameters>
          </bpsim:ElementParameters>
        </bpsim:Scenario>
      </bpsim:BPSimData>
    </bpmn2:extensionElements>
    <bpmn2:source>_RPvt8FHxED6nwbQNPNMzsw</bpmn2:source>
    <bpmn2:target>_RPvt8FHxED6nwbQNPNMzsw</bpmn2:target>
  </bpmn2:relationship>
</bpmn2:definitions>