# 設定應用程式名稱
spring.application.name=ETCH

# 引用共用的配置
spring.config.import=classpath:application-common-bank.properties

# spring 需要認證的URL
security.protected.urls=/home,/common/**,/emitter/**


# --- DataSource (auto-registered by COMMON-DATA-STARTER) ---
jcs.datasource.jndi.tw=jdbc/ETCH
# 若未來有第二個資料庫，新增另一條：
# jcs.datasource.jndi.us=jdbc/NJCICUSDB

# 僅單一資料庫時可不啟用（或保留預設不設定）
jcs.datasource.routing.enabled=false
