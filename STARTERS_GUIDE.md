# COMMON-WEB-STARTER 與 COMMON-DATA-STARTER 使用指南

本文檔說明本專案兩個 Spring Boot 自動組態 Starter 的安裝、啟用、覆寫擴充方式與操作範例，協助應用模組快速採用統一的 Web 與資料層能力。

- 適用版本：目前專案（parent POM）所管理之 Spring Boot 與 Starter 版本
- 模組位置：
  - COMMON-WEB-STARTER
  - COMMON-DATA-STARTER

---

## 目錄
- 概觀
- 安裝方式（pom 依賴）
- COMMON-WEB-STARTER
  - 自動註冊內容
  - 如何覆寫/調整
  - 常見問題
- COMMON-DATA-STARTER
  - 自動註冊內容
  - 啟用條件
  - 應用端需要做什麼
  - 覆寫/停用方式
  - 常見問題
- 最小可行設定清單（Checklist）

---

## 概觀
- COMMON-WEB-STARTER
  - 提供共用 Web 組態（SiteMesh Filter、FreeMarker 設定與 ViewResolver、JSP ViewResolver、攔截器自動註冊）。
  - 僅在 Web 環境生效；全部以 ConditionalOnMissingBean 設計，應用端可覆寫。
- COMMON-DATA-STARTER
  - 提供資料層骨架 DataAutoConfiguration 與「多資料來源動態路由」DataRoutingAutoConfiguration。
  - 路由僅在屬性 jcs.datasource.routing.enabled=true 且存在兩個以上 DataSource 時自動建立，並標記為 @Primary。

---

## 安裝方式（pom 依賴）
在任一應用模組（例如 EJCIC/EJMN/ETCH）加入下列依賴（目前專案已加入）：

```xml
<!-- COMMON-WEB-STARTER -->
<dependency>
  <groupId>tcb.jcic</groupId>
  <artifactId>COMMON-WEB-STARTER</artifactId>
  <version>${project.version}</version>
</dependency>

<!-- COMMON-DATA-STARTER -->
<dependency>
  <groupId>tcb.jcic</groupId>
  <artifactId>COMMON-DATA-STARTER</artifactId>
  <version>${project.version}</version>
</dependency>
```

---

## COMMON-WEB-STARTER

### 自動註冊內容（節錄）
- SiteMesh Filter（若 classpath 存在 SiteMesh）：名稱「siteMeshFilter」，套用全域 URL pattern，優先順序高。
- FreeMarkerConfigurer：模板路徑 `/form/`、UTF-8。
- FreeMarkerViewResolver：副檔名 `.ftl`、contentType `text/html; charset=utf-8`、order=1。
- JSP InternalResourceViewResolver：路徑 `/pages/*.jsp`、order=2。
- 預設 WebMvcConfigurer：自動註冊容器中所有 `HandlerInterceptor` Bean；攔截 `/**`，排除 `/actuator/**` 與 `/assets/**`。

### 如何覆寫/調整
- 覆寫任一預設 Bean：在應用端定義相同名稱或同型別的 Bean 即可。
- 停用 SiteMesh：移除 SiteMesh 依賴（Starter 以 `@ConditionalOnClass` 防護）；或提供同名 Bean 覆寫。

範例：自訂 FreeMarker 模板路徑
```java
@Bean
public FreeMarkerConfigurer freeMarkerConfigurer() {
  FreeMarkerConfigurer c = new FreeMarkerConfigurer();
  c.setTemplateLoaderPath("/templates/");
  c.setDefaultEncoding("UTF-8");
  return c;
}
```

### 常見問題
- ViewResolver 順序：預設 FreeMarker(order=1) 在 JSP(order=2) 之前，如需調整可自訂同名 Bean 覆寫。
- 攔截器註冊：如需自訂路徑規則，提供自家 `WebMvcConfigurer` 即會覆寫 Starter 的預設註冊。

---

## COMMON-DATA-STARTER

### 自動註冊內容
- DataAutoConfiguration：資料層骨架自動組態（目前提供一個 marker Bean，可後續擴充 Repository 掃描、Tx、JdbcTemplate 等）。
- DataRoutingAutoConfiguration：條件符合時建立名稱為 `routingDataSource` 的路由資料源，並設為 `@Primary`。

關鍵程式（摘要）：
```java
@Primary
@Bean(name = "routingDataSource")
@ConditionalOnProperty(prefix = "jcs.datasource.routing", name = "enabled", havingValue = "true")
@ConditionalOnMissingBean(name = "routingDataSource")
public DataSource routingDataSource(ObjectProvider<Map<String, DataSource>> all) {
  AbstractRoutingDataSource routing = new AbstractRoutingDataSource() {
    @Override
    protected Object determineCurrentLookupKey() {
      return jcs.datasource.DataSourceHolder.getCustomerType();
    }
  };
  // 將應用端現有 DataSource Bean 聚合為 targets，並選第一個作為 default
  return routing;
}
```

### 啟用條件
- 設定屬性（application.yml / application.properties）：
```yaml
jcs:
  datasource:
    routing:
      enabled: true
```
- 存在兩個以上的具名 DataSource Bean（Starter 才會實際建立路由）。

### 應用端需要做什麼
1) 宣告具名 DataSource Bean（例如 tw、us）。Starter 會自動聚合成 targetDataSources。

以 JNDI 為例（專案內已完成 tw 範例）：
```java
@Bean(name = "tw")
public DataSource twDataSource() throws Exception {
  JndiObjectFactoryBean jndi = new JndiObjectFactoryBean();
  jndi.setJndiName("jdbc/ELTW"); // 各應用依實際 JNDI 名稱調整
  jndi.setProxyInterface(DataSource.class);
  jndi.afterPropertiesSet();
  return (DataSource) jndi.getObject();
}
```

> 替代方案：使用屬性自動註冊 JNDI DataSource（免寫程式碼）
>
> application.properties
>
> jcs.datasource.jndi.tw=jdbc/ELTW
> jcs.datasource.jndi.us=jdbc/NJCICUSDB
>
> 說明：
> - 由 COMMON-DATA-STARTER 自動建立名為 `tw`、`us` 的 DataSource Bean
> - 若應用端已宣告同名 Bean，Starter 會尊重既有 Bean（不覆寫）
> - 依然需設定 `jcs.datasource.routing.enabled=true` 並提供 2+ 個 DataSource 才會啟用路由


2) 在需要切換資料來源時設定路由鍵（ThreadLocal），用畢務必清除：
```java
DataSourceHolder.setCustomerType("us");
try {
  // 業務邏輯（此期間將使用名為 "us" 的 DataSource）
} finally {
  DataSourceHolder.clearCustomerType();
}
```


3) 替代方案：使用註解 @UseDataSource（需在應用端加入 spring-boot-starter-aop 依賴）
```java
@UseDataSource("us")
public void doWithUs() {
  // 此方法執行期間自動切換至名為 "us" 的 DataSource
}
```

### 覆寫/停用方式
- 覆寫 routingDataSource：在應用端自定義名為 `routingDataSource` 的 Bean，即會略過 Starter 預設。
- 停用路由：將 `jcs.datasource.routing.enabled=false`（或不設定）；或僅保留一個 DataSource。

### 常見問題
- 只有一個 DataSource：Starter 不會建立 `routingDataSource`（避免多此一舉）。
- routing.enabled 行為：
  - 單一 DataSource 時：即使設為 true 也不會建立 `routingDataSource`，行為等同未啟用；建議設為 false 或不設定
  - 多個 DataSource 時：需設為 true 才會建立 `routingDataSource`（@Primary），並可用 ThreadLocal 或 `@UseDataSource` 切換

- 切換未生效：確認 Bean 名稱（如 `tw`、`us`）與 `DataSourceHolder.setCustomerType(...)` 值一致；確認屬性已開啟且有 2+ 個 DataSource。
- ThreadLocal 清理：請務必以 `try/finally` 呼叫 `clearCustomerType()`，避免跨請求汙染。

---

## 最小可行設定清單（Checklist）

### 預設資料源的指定（選用）
- 若希望明確指定預設資料源，可設定：`jcs.datasource.default=<beanName>`（例如：tw）
- 若未設定，則以屬性宣告順序中的第一個資料源為預設

### 依 HTTP Header 自動切換（選用）
- 啟用方式：
  - `jcs.datasource.header.enabled=true`
  - `jcs.datasource.header.name=X-DS`（可調整）
- 行為：
  - 若請求帶有對應 Header 值（如 X-DS: us），會在該請求期間自動套用對應資料源（巢狀安全，與方法層級註解可並用）
  - 方法層級 `@UseDataSource` 仍可覆寫請求層級設定，並於方法結束後自動還原

- [x] 引入依賴：COMMON-WEB-STARTER、COMMON-DATA-STARTER
- [x] 提供至少兩個 DataSource（二選一）
  - 宣告具名 DataSource Bean（如 `tw`、`us`）；或
  - 以屬性 `jcs.datasource.jndi.*` 設定對應的 JNDI 名稱
- [x] 設定屬性 `jcs.datasource.routing.enabled=true`
- [x] 在需要切換場景使用 `DataSourceHolder.setCustomerType(...)` 包裹業務邏輯

---

如需我提供：
- 以 AOP 或註解自動切換資料源的範例、
- SiteMesh 進階設定與 FreeMarker 自訂配置、
- 單元測試與整合測試模板，

請告訴我你的偏好與場景，我可以直接補上最貼近的實作。



## 常見錯誤排除（Troubleshooting）

- Header 指向不存在的資料源（例如 X-DS: jp，但系統僅有 tw/us）
  - 行為：會退回使用 routing 的預設資料源（`jcs.datasource.default` 指定者；未指定則為宣告順序第一個）。
  - 建議：
    - 檢查實際宣告之 DataSource 名稱與 Header 值是否相符（大小寫一致）。
    - 若需避免誤用，可在應用端自行加入白名單檢查（例如在 Interceptor/Filter 中驗證），目前 Starter 不強制限制。

- 僅一個資料源時設定了 X-DS（例如 us）
  - 行為：由於容器僅有一個 DataSource，不會建立 routingDataSource；Header 不產生效果，仍使用唯一資料源。
  - 建議：此情境不需開啟 routing；未來若新增第二個資料源再將 `jcs.datasource.routing.enabled=true`。

- Header 值為空或未提供
  - 行為：不進行 push，沿用外層或預設資料源；若方法層級有 @UseDataSource 則以方法為準。

- 方法層級與請求層級同時存在不同設定
  - 行為：方法層級 @UseDataSource 具較高優先序；切面以 push/pop 巢狀處理，方法完成後會自動還原請求層級狀態。
