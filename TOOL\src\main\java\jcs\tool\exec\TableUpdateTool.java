package jcs.tool.exec;

import java.util.List;
import java.util.Scanner;

import jcs.tool.bean.WorkBean;
import jcs.tool.generator.DaoGenerator;
import jcs.tool.generator.ListGenerator;
import jcs.tool.generator.OrmGenerator;
import jcs.tool.generator.SqlGenerator;
import jcs.tool.generator.TableConstantGenerator;

/**
 * 表格異動工具。
 */
public class TableUpdateTool extends Tool{
	
	public static void main(String[] args) throws Exception{
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		List<WorkBean> workList = getWork(sc, tableupdate, args);
		// 區分執行專案
		for(WorkBean bean : workList){
			OrmGenerator.main(new String[]{bean.getCode()});
			DaoGenerator.main(new String[]{bean.getCode()});
			SqlGenerator.main(new String[]{bean.getCode()});
			ListGenerator.main(new String[]{bean.getCode()});
		}
		// 不區分專案一律全部重新產出
		TableConstantGenerator.main(new String[]{workList.size() > 0 ? "Y" : "N"});
	}
}
