package com.bean;

import java.io.Serializable;

import java.util.Date;

import jakarta.persistence.*;
import jcs.basebean.JcsSerialBean;

/**
 * 企金案件主檔。
 */
@Entity
@Table(name = "TEMPMAS")
@IdClass(TempMas.Key.class)
public class TempMas extends JcsSerialBean{
	private static final long serialVersionUID = 1L;
	
	/** 建構子 */
	public TempMas(){}
	
	/** 建構子 */
	public TempMas(String receno){
		this.receno = receno;
	}
	
	/** 收件編號 VARCHAR(16) */
	@Id
	@Column(name = "RECENO", columnDefinition = "VARCHAR(16)", nullable = false)
	private String receno;
	
	/** 營業單位代碼 VARCHAR(6) */
	@Column(name = "BRANCH", columnDefinition = "VARCHAR(6)")
	private String branch;
	
	/** 申請單位代碼 VARCHAR(6) */
	@Column(name = "APLBRANCH", columnDefinition = "VARCHAR(6)")
	private String aplbranch;
	
	/** 區營運處代碼 VARCHAR(6) */
	@Column(name = "CRC", columnDefinition = "VARCHAR(6)")
	private String crc;
	
	/** 區審中心代碼 VARCHAR(6) */
	@Column(name = "EACRC", columnDefinition = "VARCHAR(6)")
	private String eacrc;
	
	/** 統編 VARCHAR(10) */
	@Column(name = "CID", columnDefinition = "VARCHAR(10)")
	private String cid;
	
	/** 重覆序號 VARCHAR(1) */
	@Column(name = "DUPCD", columnDefinition = "VARCHAR(1)")
	private String dupcd;
	
	/** 授信戶名稱 VARCHAR(300) */
	@Column(name = "APPNAME", columnDefinition = "VARCHAR(300)")
	private String appname;
	
	/** 授信戶筆數 DECIMAL(3) */
	@Column(name = "CCNT", columnDefinition = "DECIMAL(3)", precision = 3)
	private Integer ccnt;
	
	/** 案件狀態 VARCHAR(2) */
	@Column(name = "APPRSTU", columnDefinition = "VARCHAR(2)")
	private String apprstu;
	
	/** 網路申請序號 VARCHAR(20) */
	@Column(name = "NETNO", columnDefinition = "VARCHAR(20)")
	private String netno;
	
	/** 網路申請貸款類別 VARCHAR(2) */
	@Column(name = "NETTP", columnDefinition = "VARCHAR(2)")
	private String nettp;
	
	/** 申請日期 DATE */
	@Column(name = "APPDATE", columnDefinition = "DATE")
	private Date appdate;
	
	/** 申請書大類 VARCHAR(2) */
	@Column(name = "CASETYPE", columnDefinition = "VARCHAR(2)")
	private String casetype;
	
	/** 申請書小類 VARCHAR(2) */
	@Column(name = "CASEKIND", columnDefinition = "VARCHAR(2)")
	private String casekind;
	
	/** 申請類別 VARCHAR(20) */
	@Column(name = "APPTP", columnDefinition = "VARCHAR(20)")
	private String apptp;
	
	/** 申請類別-授信變更 VARCHAR(30) */
	@Column(name = "APPTPC", columnDefinition = "VARCHAR(30)")
	private String apptpc;
	
	/** 申請類別-其他屬72-2 VARCHAR(10) */
	@Column(name = "APPTP722", columnDefinition = "VARCHAR(10)")
	private String apptp722;
	
	/** 新貸情形 VARCHAR(1) */
	@Column(name = "NEWSTUS", columnDefinition = "VARCHAR(1)")
	private String newstus;
	
	/** 是否涉及利(費)率調降 VARCHAR(1) */
	@Column(name = "CUTINTER", columnDefinition = "VARCHAR(1)")
	private String cutinter;
	
	/** 下次追蹤(監控)日期 DATE */
	@Column(name = "NXFLODT", columnDefinition = "DATE")
	private Date nxflodt;
	
	/** 本次辦理預警檢核原因 VARCHAR(60) */
	@Column(name = "EWREN", columnDefinition = "VARCHAR(60)")
	private String ewren;
	
	/** 預警戶種類 VARCHAR(1) */
	@Column(name = "EWFG", columnDefinition = "VARCHAR(1)")
	private String ewfg;
	
	/** 前次通報日期 DATE */
	@Column(name = "LRPTDT", columnDefinition = "DATE")
	private Date lrptdt;
	
	/** 審核層級(分行) VARCHAR(3) */
	@Column(name = "APRLVLB", columnDefinition = "VARCHAR(3)")
	private String aprlvlb;
	
	/** 報備層級 VARCHAR(3) */
	@Column(name = "APRLVLR", columnDefinition = "VARCHAR(3)")
	private String aprlvlr;
	
	/** 審核權限 VARCHAR(1) */
	@Column(name = "APRUNIT", columnDefinition = "VARCHAR(1)")
	private String aprunit;
	
	/** 解除年審註記 CHAR(1) */
	@Column(name = "RLSREVFG", columnDefinition = "CHAR(1)")
	private String rlsrevfg;
	
	/** 年度審核到期日(本次) DATE */
	@Column(name = "ANNREVD", columnDefinition = "DATE")
	private Date annrevd;
	
	/** 依規免辦年審 CHAR(1) */
	@Column(name = "EXEANNREV", columnDefinition = "CHAR(1)")
	private String exeannrev;
	
	/** 免辦年審原因 VARCHAR(20) */
	@Column(name = "EXEANNREVR", columnDefinition = "VARCHAR(20)")
	private String exeannrevr;
	
	/** 額度最後動用期限 DATE */
	@Column(name = "UPERIOD", columnDefinition = "DATE")
	private Date uperiod;
	
	/** 年度審核到期日(申請/變更後) DATE */
	@Column(name = "ANNREVDA", columnDefinition = "DATE")
	private Date annrevda;
	
	/** 是否先轉送資金營運處 CHAR(1) */
	@Column(name = "PRODERIV", columnDefinition = "CHAR(1)")
	private String proderiv;
	
	/** 本案是否僅申請下列項目？特定授權、商務卡、出口押匯、輕微授信變更 CHAR(1) */
	@Column(name = "BCSA", columnDefinition = "CHAR(1)")
	private String bcsa;
	
	/** 提會案件 CHAR(1) */
	@Column(name = "METCASE", columnDefinition = "CHAR(1)")
	private String metcase;
	
	/** 本案是否轉呈總行或區審中心 CHAR(1) */
	@Column(name = "SUMTFG", columnDefinition = "CHAR(1)")
	private String sumtfg;
	
	/** 會議議程系統編號 VARCHAR(12) */
	@Column(name = "METNO", columnDefinition = "VARCHAR(12)")
	private String metno;
	
	/** 案件性質 DECIMAL(3) */
	@Column(name = "METCSTYPE", columnDefinition = "DECIMAL(3)", precision = 3)
	private Integer metcstype;
	
	/** 提會案次 DECIMAL(2) */
	@Column(name = "METSEQ", columnDefinition = "DECIMAL(2)", precision = 2)
	private Integer metseq;
	
	/** 轉請總行審議 CHAR(1) */
	@Column(name = "APPR", columnDefinition = "CHAR(1)")
	private String appr;
	
	/** 轉請核定申請日 DATE */
	@Column(name = "APPDATET", columnDefinition = "DATE")
	private Date appdatet;
	
	/** 轉請核定審核層級 VARCHAR(3) */
	@Column(name = "APRLVLT", columnDefinition = "VARCHAR(3)")
	private String aprlvlt;
	
	/** 轉請核定放行日 DATE */
	@Column(name = "APPRDTT", columnDefinition = "DATE")
	private Date apprdtt;
	
	/** 第一次傳送區審/總行日期 TIMESTAMP */
	@Column(name = "FIRSTDT", columnDefinition = "TIMESTAMP")
	private Date firstdt;
	
	/** 最近傳送區審/總行日期 TIMESTAMP */
	@Column(name = "SENDDT", columnDefinition = "TIMESTAMP")
	private Date senddt;
	
	/** 最終審核層級 VARCHAR(3) */
	@Column(name = "APRLVLH", columnDefinition = "VARCHAR(3)")
	private String aprlvlh;
	
	/** 核定層級 VARCHAR(3) */
	@Column(name = "APRLVLP", columnDefinition = "VARCHAR(3)")
	private String aprlvlp;
	
	/** 本案批覆完成日期 DATE */
	@Column(name = "APPRDT", columnDefinition = "DATE")
	private Date apprdt;
	
	/** 本案准駁結果 VARCHAR(3) */
	@Column(name = "APPRANS", columnDefinition = "VARCHAR(3)")
	private String apprans;
	
	/** 額度動用期限 DATE */
	@Column(name = "SPC", columnDefinition = "DATE")
	private Date spc;
	
	/** 本案核定者員編 VARCHAR(10) */
	@Column(name = "APPRID", columnDefinition = "VARCHAR(10)")
	private String apprid;
	
	/** 本案核定者名稱 VARCHAR(30) */
	@Column(name = "APPRNAME", columnDefinition = "VARCHAR(30)")
	private String apprname;
	
	/** 婉拒原因代碼 VARCHAR(2) */
	@Column(name = "REJCODE", columnDefinition = "VARCHAR(2)")
	private String rejcode;
	
	/** 撤件原因代碼 VARCHAR(2) */
	@Column(name = "CANCELCODE", columnDefinition = "VARCHAR(2)")
	private String cancelcode;
	
	/** 申請書文件亂碼 VARCHAR(14) */
	@Column(name = "RANCODE", columnDefinition = "VARCHAR(14)")
	private String rancode;
	
	/** 轉請總行核定申請書文件亂碼 VARCHAR(14) */
	@Column(name = "TRANCODE", columnDefinition = "VARCHAR(14)")
	private String trancode;
	
	/** 審查意見書/提案書文件亂碼 VARCHAR(14) */
	@Column(name = "SRANCODE", columnDefinition = "VARCHAR(14)")
	private String srancode;
	
	/** 准駁通知書文件亂碼 VARCHAR(14) */
	@Column(name = "NRANCODE", columnDefinition = "VARCHAR(14)")
	private String nrancode;
	
	/** 目前處理人員 VARCHAR(10) */
	@Column(name = "OWNERID", columnDefinition = "VARCHAR(10)")
	private String ownerid;
	
	/** 目前處理單位 VARCHAR(6) */
	@Column(name = "OWNERDEPT", columnDefinition = "VARCHAR(6)")
	private String ownerdept;
	
	/** 分行經辦 VARCHAR(10) */
	@Column(name = "AOID", columnDefinition = "VARCHAR(10)")
	private String aoid;
	
	/** 審查員 VARCHAR(10) */
	@Column(name = "SRID", columnDefinition = "VARCHAR(10)")
	private String srid;
	
	/** 作業科經辦 VARCHAR(10) */
	@Column(name = "OPID", columnDefinition = "VARCHAR(10)")
	private String opid;
	
	/** 是否舊案轉入 CHAR(1) */
	@Column(name = "ISOLD", columnDefinition = "CHAR(1)")
	private String isold;
	
	/** 程式註記 VARCHAR(1) */
	@Column(name = "FLOWFLG", columnDefinition = "VARCHAR(1)")
	private String flowflg;
	
	/** 文件建立者 VARCHAR(10) */
	@Column(name = "CRAID", columnDefinition = "VARCHAR(10)")
	private String craid;
	
	/** 文件建立日期 TIMESTAMP */
	@Column(name = "CRATIME", columnDefinition = "TIMESTAMP")
	private Date cratime;
	
	/** 資料修改者 VARCHAR(10) */
	@Column(name = "UPDATER", columnDefinition = "VARCHAR(10)")
	private String updater;
	
	/** 資料修改日期 TIMESTAMP */
	@Column(name = "UPDTIME", columnDefinition = "TIMESTAMP")
	private Date updtime;
	
	/** 資料源 VARCHAR(2) */
	@Column(name = "DATASOURCE", columnDefinition = "VARCHAR(2)")
	private String datasource;
	
	/** 東京分行經理權限 CHAR(1) */
	@Column(name = "APRLVLTK", columnDefinition = "CHAR(1)")
	private String aprlvltk;
	
	/** get收件編號 */
	public String getReceno(){
		return this.receno;
	}
	
	/** set收件編號 */
	public void setReceno(String value){
		this.receno = value;
	}
	
	/** get營業單位代碼 */
	public String getBranch(){
		return this.branch;
	}
	
	/** set營業單位代碼 */
	public void setBranch(String value){
		this.branch = value;
	}
	
	/** get申請單位代碼 */
	public String getAplbranch(){
		return this.aplbranch;
	}
	
	/** set申請單位代碼 */
	public void setAplbranch(String value){
		this.aplbranch = value;
	}
	
	/** get區營運處代碼 */
	public String getCrc(){
		return this.crc;
	}
	
	/** set區營運處代碼 */
	public void setCrc(String value){
		this.crc = value;
	}
	
	/** get區審中心代碼 */
	public String getEacrc(){
		return this.eacrc;
	}
	
	/** set區審中心代碼 */
	public void setEacrc(String value){
		this.eacrc = value;
	}
	
	/** get統編 */
	public String getCid(){
		return this.cid;
	}
	
	/** set統編 */
	public void setCid(String value){
		this.cid = value;
	}
	
	/** get重覆序號 */
	public String getDupcd(){
		return this.dupcd;
	}
	
	/** set重覆序號 */
	public void setDupcd(String value){
		this.dupcd = (value == null ? "" : value);
	}
	
	/** get授信戶名稱 */
	public String getAppname(){
		return this.appname;
	}
	
	/** set授信戶名稱 */
	public void setAppname(String value){
		this.appname = value;
	}
	
	/** get授信戶筆數 */
	public Integer getCcnt(){
		return this.ccnt;
	}
	
	/** set授信戶筆數 */
	public void setCcnt(Integer value){
		this.ccnt = value;
	}
	
	/** get案件狀態 */
	public String getApprstu(){
		return this.apprstu;
	}
	
	/** set案件狀態 */
	public void setApprstu(String value){
		this.apprstu = value;
	}
	
	/** get網路申請序號 */
	public String getNetno(){
		return this.netno;
	}
	
	/** set網路申請序號 */
	public void setNetno(String value){
		this.netno = value;
	}
	
	/** get網路申請貸款類別 */
	public String getNettp(){
		return this.nettp;
	}
	
	/** set網路申請貸款類別 */
	public void setNettp(String value){
		this.nettp = value;
	}
	
	/** get申請日期 */
	public Date getAppdate(){
		return this.appdate;
	}
	
	/** set申請日期 */
	public void setAppdate(Date value){
		this.appdate = value;
	}
	
	/** get申請書大類 */
	public String getCasetype(){
		return this.casetype;
	}
	
	/** set申請書大類 */
	public void setCasetype(String value){
		this.casetype = value;
	}
	
	/** get申請書小類 */
	public String getCasekind(){
		return this.casekind;
	}
	
	/** set申請書小類 */
	public void setCasekind(String value){
		this.casekind = value;
	}
	
	/** get申請類別 */
	public String getApptp(){
		return this.apptp;
	}
	
	/** set申請類別 */
	public void setApptp(String value){
		this.apptp = value;
	}
	
	/** get申請類別-授信變更 */
	public String getApptpc(){
		return this.apptpc;
	}
	
	/** set申請類別-授信變更 */
	public void setApptpc(String value){
		this.apptpc = value;
	}
	
	/** get申請類別-其他屬72-2 */
	public String getApptp722(){
		return this.apptp722;
	}
	
	/** set申請類別-其他屬72-2 */
	public void setApptp722(String value){
		this.apptp722 = value;
	}
	
	/** get新貸情形 */
	public String getNewstus(){
		return this.newstus;
	}
	
	/** set新貸情形 */
	public void setNewstus(String value){
		this.newstus = value;
	}
	
	/** get是否涉及利(費)率調降 */
	public String getCutinter(){
		return this.cutinter;
	}
	
	/** set是否涉及利(費)率調降 */
	public void setCutinter(String value){
		this.cutinter = value;
	}
	
	/** get下次追蹤(監控)日期 */
	public Date getNxflodt(){
		return this.nxflodt;
	}
	
	/** set下次追蹤(監控)日期 */
	public void setNxflodt(Date value){
		this.nxflodt = value;
	}
	
	/** get本次辦理預警檢核原因 */
	public String getEwren(){
		return this.ewren;
	}
	
	/** set本次辦理預警檢核原因 */
	public void setEwren(String value){
		this.ewren = value;
	}
	
	/** get預警戶種類 */
	public String getEwfg(){
		return this.ewfg;
	}
	
	/** set預警戶種類 */
	public void setEwfg(String value){
		this.ewfg = value;
	}
	
	/** get前次通報日期 */
	public Date getLrptdt(){
		return this.lrptdt;
	}
	
	/** set前次通報日期 */
	public void setLrptdt(Date value){
		this.lrptdt = value;
	}
	
	/** get審核層級(分行) */
	public String getAprlvlb(){
		return this.aprlvlb;
	}
	
	/** set審核層級(分行) */
	public void setAprlvlb(String value){
		this.aprlvlb = value;
	}
	
	/** get報備層級 */
	public String getAprlvlr(){
		return this.aprlvlr;
	}
	
	/** set報備層級 */
	public void setAprlvlr(String value){
		this.aprlvlr = value;
	}
	
	/** get審核權限 */
	public String getAprunit(){
		return this.aprunit;
	}
	
	/** set審核權限 */
	public void setAprunit(String value){
		this.aprunit = value;
	}
	
	/** get解除年審註記 */
	public String getRlsrevfg(){
		return this.rlsrevfg;
	}
	
	/** set解除年審註記 */
	public void setRlsrevfg(String value){
		this.rlsrevfg = value;
	}
	
	/** get年度審核到期日(本次) */
	public Date getAnnrevd(){
		return this.annrevd;
	}
	
	/** set年度審核到期日(本次) */
	public void setAnnrevd(Date value){
		this.annrevd = value;
	}
	
	/** get依規免辦年審 */
	public String getExeannrev(){
		return this.exeannrev;
	}
	
	/** set依規免辦年審 */
	public void setExeannrev(String value){
		this.exeannrev = value;
	}
	
	/** get免辦年審原因 */
	public String getExeannrevr(){
		return this.exeannrevr;
	}
	
	/** set免辦年審原因 */
	public void setExeannrevr(String value){
		this.exeannrevr = value;
	}
	
	/** get額度最後動用期限 */
	public Date getUperiod(){
		return this.uperiod;
	}
	
	/** set額度最後動用期限 */
	public void setUperiod(Date value){
		this.uperiod = value;
	}
	
	/** get年度審核到期日(申請/變更後) */
	public Date getAnnrevda(){
		return this.annrevda;
	}
	
	/** set年度審核到期日(申請/變更後) */
	public void setAnnrevda(Date value){
		this.annrevda = value;
	}
	
	/** get是否先轉送資金營運處 */
	public String getProderiv(){
		return this.proderiv;
	}
	
	/** set是否先轉送資金營運處 */
	public void setProderiv(String value){
		this.proderiv = value;
	}
	
	/** get本案是否僅申請下列項目？特定授權、商務卡、出口押匯、輕微授信變更 */
	public String getBcsa(){
		return this.bcsa;
	}
	
	/** set本案是否僅申請下列項目？特定授權、商務卡、出口押匯、輕微授信變更 */
	public void setBcsa(String value){
		this.bcsa = value;
	}
	
	/** get提會案件 */
	public String getMetcase(){
		return this.metcase;
	}
	
	/** set提會案件 */
	public void setMetcase(String value){
		this.metcase = value;
	}
	
	/** get本案是否轉呈總行或區審中心 */
	public String getSumtfg(){
		return this.sumtfg;
	}
	
	/** set本案是否轉呈總行或區審中心 */
	public void setSumtfg(String value){
		this.sumtfg = value;
	}
	
	/** get會議議程系統編號 */
	public String getMetno(){
		return this.metno;
	}
	
	/** set會議議程系統編號 */
	public void setMetno(String value){
		this.metno = value;
	}
	
	/** get案件性質 */
	public Integer getMetcstype(){
		return this.metcstype;
	}
	
	/** set案件性質 */
	public void setMetcstype(Integer value){
		this.metcstype = value;
	}
	
	/** get提會案次 */
	public Integer getMetseq(){
		return this.metseq;
	}
	
	/** set提會案次 */
	public void setMetseq(Integer value){
		this.metseq = value;
	}
	
	/** get轉請總行審議 */
	public String getAppr(){
		return this.appr;
	}
	
	/** set轉請總行審議 */
	public void setAppr(String value){
		this.appr = value;
	}
	
	/** get轉請核定申請日 */
	public Date getAppdatet(){
		return this.appdatet;
	}
	
	/** set轉請核定申請日 */
	public void setAppdatet(Date value){
		this.appdatet = value;
	}
	
	/** get轉請核定審核層級 */
	public String getAprlvlt(){
		return this.aprlvlt;
	}
	
	/** set轉請核定審核層級 */
	public void setAprlvlt(String value){
		this.aprlvlt = value;
	}
	
	/** get轉請核定放行日 */
	public Date getApprdtt(){
		return this.apprdtt;
	}
	
	/** set轉請核定放行日 */
	public void setApprdtt(Date value){
		this.apprdtt = value;
	}
	
	/** get第一次傳送區審/總行日期 */
	public Date getFirstdt(){
		return this.firstdt;
	}
	
	/** set第一次傳送區審/總行日期 */
	public void setFirstdt(Date value){
		this.firstdt = value;
	}
	
	/** get最近傳送區審/總行日期 */
	public Date getSenddt(){
		return this.senddt;
	}
	
	/** set最近傳送區審/總行日期 */
	public void setSenddt(Date value){
		this.senddt = value;
	}
	
	/** get最終審核層級 */
	public String getAprlvlh(){
		return this.aprlvlh;
	}
	
	/** set最終審核層級 */
	public void setAprlvlh(String value){
		this.aprlvlh = value;
	}
	
	/** get核定層級 */
	public String getAprlvlp(){
		return this.aprlvlp;
	}
	
	/** set核定層級 */
	public void setAprlvlp(String value){
		this.aprlvlp = value;
	}
	
	/** get本案批覆完成日期 */
	public Date getApprdt(){
		return this.apprdt;
	}
	
	/** set本案批覆完成日期 */
	public void setApprdt(Date value){
		this.apprdt = value;
	}
	
	/** get本案准駁結果 */
	public String getApprans(){
		return this.apprans;
	}
	
	/** set本案准駁結果 */
	public void setApprans(String value){
		this.apprans = value;
	}
	
	/** get額度動用期限 */
	public Date getSpc(){
		return this.spc;
	}
	
	/** set額度動用期限 */
	public void setSpc(Date value){
		this.spc = value;
	}
	
	/** get本案核定者員編 */
	public String getApprid(){
		return this.apprid;
	}
	
	/** set本案核定者員編 */
	public void setApprid(String value){
		this.apprid = value;
	}
	
	/** get本案核定者名稱 */
	public String getApprname(){
		return this.apprname;
	}
	
	/** set本案核定者名稱 */
	public void setApprname(String value){
		this.apprname = value;
	}
	
	/** get婉拒原因代碼 */
	public String getRejcode(){
		return this.rejcode;
	}
	
	/** set婉拒原因代碼 */
	public void setRejcode(String value){
		this.rejcode = value;
	}
	
	/** get撤件原因代碼 */
	public String getCancelcode(){
		return this.cancelcode;
	}
	
	/** set撤件原因代碼 */
	public void setCancelcode(String value){
		this.cancelcode = value;
	}
	
	/** get申請書文件亂碼 */
	public String getRancode(){
		return this.rancode;
	}
	
	/** set申請書文件亂碼 */
	public void setRancode(String value){
		this.rancode = value;
	}
	
	/** get轉請總行核定申請書文件亂碼 */
	public String getTrancode(){
		return this.trancode;
	}
	
	/** set轉請總行核定申請書文件亂碼 */
	public void setTrancode(String value){
		this.trancode = value;
	}
	
	/** get審查意見書/提案書文件亂碼 */
	public String getSrancode(){
		return this.srancode;
	}
	
	/** set審查意見書/提案書文件亂碼 */
	public void setSrancode(String value){
		this.srancode = value;
	}
	
	/** get准駁通知書文件亂碼 */
	public String getNrancode(){
		return this.nrancode;
	}
	
	/** set准駁通知書文件亂碼 */
	public void setNrancode(String value){
		this.nrancode = value;
	}
	
	/** get目前處理人員 */
	public String getOwnerid(){
		return this.ownerid;
	}
	
	/** set目前處理人員 */
	public void setOwnerid(String value){
		this.ownerid = value;
	}
	
	/** get目前處理單位 */
	public String getOwnerdept(){
		return this.ownerdept;
	}
	
	/** set目前處理單位 */
	public void setOwnerdept(String value){
		this.ownerdept = value;
	}
	
	/** get分行經辦 */
	public String getAoid(){
		return this.aoid;
	}
	
	/** set分行經辦 */
	public void setAoid(String value){
		this.aoid = value;
	}
	
	/** get審查員 */
	public String getSrid(){
		return this.srid;
	}
	
	/** set審查員 */
	public void setSrid(String value){
		this.srid = value;
	}
	
	/** get作業科經辦 */
	public String getOpid(){
		return this.opid;
	}
	
	/** set作業科經辦 */
	public void setOpid(String value){
		this.opid = value;
	}
	
	/** get是否舊案轉入 */
	public String getIsold(){
		return this.isold;
	}
	
	/** set是否舊案轉入 */
	public void setIsold(String value){
		this.isold = value;
	}
	
	/** get程式註記 */
	public String getFlowflg(){
		return this.flowflg;
	}
	
	/** set程式註記 */
	public void setFlowflg(String value){
		this.flowflg = value;
	}
	
	/** get文件建立者 */
	public String getCraid(){
		return this.craid;
	}
	
	/** set文件建立者 */
	public void setCraid(String value){
		this.craid = value;
	}
	
	/** get文件建立日期 */
	public Date getCratime(){
		return this.cratime;
	}
	
	/** set文件建立日期 */
	public void setCratime(Date value){
		this.cratime = value;
	}
	
	/** get資料修改者 */
	public String getUpdater(){
		return this.updater;
	}
	
	/** set資料修改者 */
	public void setUpdater(String value){
		this.updater = value;
	}
	
	/** get資料修改日期 */
	public Date getUpdtime(){
		return this.updtime;
	}
	
	/** set資料修改日期 */
	public void setUpdtime(Date value){
		this.updtime = value;
	}
	
	/** get資料源 */
	public String getDatasource(){
		return this.datasource;
	}
	
	/** set資料源 */
	public void setDatasource(String value){
		this.datasource = value;
	}
	
	/** get東京分行經理權限 */
	public String getAprlvltk(){
		return this.aprlvltk;
	}
	
	/** set東京分行經理權限 */
	public void setAprlvltk(String value){
		this.aprlvltk = value;
	}
	
	/** Key */
	public static class Key implements Serializable{
		private static final long serialVersionUID = 1L;
		
		public Key(){}
		
		public Key(String receno){
			this.receno = receno;
		}
		
		/** 收件編號 */
		private String receno;
		
		public String getReceno(){
			return this.receno;
		}
		
		public void setReceno(String value){
			this.receno = value;
		}
		
		@Override
		public int hashCode(){
			final int prime = 31;
			int result = 1;
			result = prime * result + ((receno == null) ? 0 : receno.hashCode());
			return result;
		}
		
		@Override
		public boolean equals(Object obj){
			if(this == obj)
				return true;
			if(obj == null)
				return false;
			if(getClass() != obj.getClass())
				return false;
			Key other = (Key)obj;
			if(receno == null){
				if(other.receno != null)
					return false;
			}else if(!receno.equals(other.receno))
				return false;
			return true;
		}
	}
}
