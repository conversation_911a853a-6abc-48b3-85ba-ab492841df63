package jcs.datasource;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * UseDataSource
 *
 * 函式級註解：在方法（或類別）上標註使用的資料來源名稱（如 "tw"、"us"）。
 * 啟用 jcs.datasource.routing.enabled 且存在 2+ 個 DataSource 時，AOP 將於方法執行期間
 * 透過 ThreadLocal 設定路由鍵，並於 finally 清理。
 */
@Documented
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface UseDataSource {
    /**
     * 指定要使用的資料來源 Bean 名稱。
     */
    String value();
}

