package jcs.tool.exec;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JRadioButton;

import jcs.tool.bean.WorkBean;

/**
 * 同步程式工具。
 */
public class SyncSourceTool extends Tool{
	private static final String jsPath = "\\src\\main\\webapp\\assets\\basic\\js";
	
	private static final String cssPath = "\\src\\main\\webapp\\assets\\basic\\css";
	
	private static final String tldPath = "\\src\\main\\webapp\\WEB-INF\\tld";
	
	// public static void main(String[] args) throws Exception{
	// Scanner sc = args.length == 0 ? new Scanner(System.in) : new
	// Scanner(args[0]);
	// List<WorkBean> workList = getWork(sc, syncsource, args);
	// Map<String, String> map = new HashMap<String, String>();
	// map.put(jsPath, "jcs.custom.js");
	// map.put(cssPath, "-");
	// map.put(tldPath, "custom.tld");
	// for(String key : map.keySet()){
	// process(workList.get(0), getAllWorkBean(), key, map.get(key));
	// }
	// }
	public static String selectProject = "1";
	
	public static void main(String[] args){
		// 創建 JFrame 實例
		JFrame frame = new JFrame("執行同步程式");
		init(frame); // 初始化s
		// Setting the width and height of frame
		frame.setSize(600, 250);
		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
		frame.setLocationRelativeTo(null); // 置中
		frame.setResizable(false); // 不可變動大小
		/*
		 * 創建面板，這個類似於 HTML 的 div 標簽 我們可以創建多個面板並在 JFrame 中指定位置
		 * 面板中我們可以添加文本字段，按鈕及其他組件。
		 */
		JPanel panel = new JPanel();
		// 添加面板
		frame.add(panel);
		/*
		 * 調用用戶定義的方法並添加組件到面板
		 */
		placeComponents(panel);
		// 設置界面可見
		frame.setVisible(true); // 設置界面可見
	}
	
	private static void placeComponents(JPanel panel){
		panel.setLayout(null); // 設置布局為 null
		// 創建 JLabel
		JLabel radioLabel = new JLabel("請選擇那個專案為複寫來源：");
		/*
		 * 這個方法定義了組件的位置。 setBounds(x, y, width, height) x 和 y 指定左上角的新位置，由 width
		 * 和 height 指定新的大小。
		 */
		radioLabel.setFont(defFont);
		radioLabel.setBounds(10, 0, 300, 30);
		panel.add(radioLabel);
		ButtonGroup radioGroup = new ButtonGroup();
		int r = 3; // 3個選項為1行
		for(Field field : Tool.SYSTYPE.class.getDeclaredFields()){
			Path path = field.getAnnotation(Path.class);
			int v = new BigDecimal(path.exec()).intValue();
			int w = v / r;
			if(v % r == 0)
				w--;
			int x = (v - (w * r + 1)) * 200 + 10, y = 30 * (w + 1);
			JRadioButton radio = new JRadioButton(field.getName(), v == 1);
			radio.setFont(defFont);
			radio.setBounds(x, y, 200, 30);
			radio.addActionListener(new ActionListener() {
				@Override
				public void actionPerformed(ActionEvent e){
					selectProject = path.exec();
				}
			});
			panel.add(radio);
			radioGroup.add(radio);
		}
		// xls2java按鈕
		JLabel buttonLabel = new JLabel("請選擇轉換功能：");
		buttonLabel.setFont(defFont);
		buttonLabel.setBounds(10, 100, 200, 30);
		// panel.add(buttonLabel);
		JButton xls2java = new JButton("開始執行");
		xls2java.setFont(defFont);
		xls2java.setBounds(10, 130, 200, 30);
		xls2java.addActionListener(new ActionListener() {
			@Override
			public void actionPerformed(ActionEvent event){
				processing(true);
				Thread t = new Thread(new Runnable() {
					public void run(){
						try{
							WorkBean workBean = getWorkBean(selectProject);
							Map<String, String> map = new HashMap<String, String>();
							map.put(jsPath, "jcs.custom.js");
							map.put(cssPath, "-");
							map.put(tldPath, "custom.tld");
							for(String key : map.keySet())
								process(workBean, getAllWorkBean(), key, map.get(key));
							alert("執行完成！");
							System.exit(0);
						}catch(Exception e){
							alert("執行失敗！");
						}finally{
							processing(false);
						}
					}
				});
				t.start();
			}
		});
		panel.add(xls2java);
	}
	
	/**
	 * 處理
	 * 
	 * @param workBean
	 * @param workBeanList
	 * @param basicPath
	 * @param ingoreFileNames
	 */
	private static void process(WorkBean workBean, List<WorkBean> workBeanList, String basicPath,
			String ingoreFileNames){
		File folder = new File(getWorkRoot() + workBean.getProject() + basicPath);
		for(WorkBean bean : workBeanList){
			if(!workBean.getProject().equals(bean.getProject()) && !"BAT".equals(bean.getProject())){
				System.out.println("[" + workBean.getProject() + "] " + workBean.getName() + " 複寫至 " + "["
						+ bean.getProject() + "] " + bean.getName());
				String path = getWorkRoot() + bean.getProject() + basicPath;
				for(File file : folder.listFiles()){
					String destFileName = path + File.separator + file.getName();
					if(file.isDirectory()){
						// ignore
					}else if(file.getName().matches("^(" + ingoreFileNames + ")$")){
						System.out.println("『複寫略過』" + destFileName);
					}else{
						File dest = new File(destFileName);
						if(dest.exists()){
							String fileMD5 = getMD5(file);
							String destMD5 = getMD5(dest);
							if(fileMD5.equals(destMD5)){
								// ignore
							}else{
								try{
									Files.copy(file.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING);
									System.out.println("『複寫完成』" + destFileName);
								}catch(IOException e){
									System.out.println("『複寫失敗』" + destFileName);
								}
							}
						}else{
							try{
								Files.copy(file.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING);
								System.out.println("『複寫完成』" + destFileName);
							}catch(IOException e){
								System.out.println("『複寫失敗』" + destFileName);
							}
						}
					}
				}
			}
		}
	}
	
	/**
	 * 輸入一個檔案類,返回檔案的MD5
	 * 
	 * @param file
	 * @return
	 */
	private static String getMD5(File file){
		String result = null;
		FileInputStream fis = null;
		try{
			MessageDigest messageDigest = MessageDigest.getInstance("MD5");
			fis = new FileInputStream(file);
			byte[] buffer = new byte[1024];
			int fr;
			while((fr = fis.read(buffer)) != -1)
				messageDigest.update(buffer, 0, fr);
			result = new BigInteger(1, messageDigest.digest()).toString(16);
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			if(fis != null){
				try{
					fis.close();
				}catch(IOException e){
					e.printStackTrace();
				}
			}
		}
		return result;
	}
}
