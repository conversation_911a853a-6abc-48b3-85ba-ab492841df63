package jcs.tool.generator;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Scanner;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import jcs.tool.exec.Tool;

/**
 * 自動生成系統選單常數。
 */
public class ParmConstantGenerator extends Tool{
	
	private static final String pam_format = "INSERT INTO ELN.ELS0PAM VALUES ({0}{1}{2}{3}{4}{5}{6}{7}{8}{9}{10}{11});";
	private static final SimpleDateFormat formatDate1 = new SimpleDateFormat("yyyy/MM/dd a hh:mm:ss");
	private static final SimpleDateFormat formatDate2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	public static void main(String[] args){
		Scanner sc = args.length == 0 ? new Scanner(System.in) : new Scanner(args[0]);
		getWork(sc, parmconstant, args);
		new ParmConstantGenerator().create(getItem());
	}
	
	/** 產出系統選單常數  */
	public void create(String item){
		System.out.println("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼");
		System.out.println("🈠 開始執行 『ParmConstantGenerator』轉換");
		switch(item){
			case "1":
				xlsxToJava();
				xlsxToDDL();
				break;
		}
		System.out.println("🈡 執行完成");
		System.out.println("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲");
	}
	
	/**
	 * XLSX轉換為JAVA。
	 */
	private static void xlsxToJava(){
		Workbook wb = null;
		InputStream in = null;
		FileWriter fw = null;
		StringBuilder path = new StringBuilder();
		StringBuilder constant = new StringBuilder();
		constant.append("package com.system.constant;").append("\n");
		constant.append("\n");
		constant.append("/**").append("\n");
		constant.append(" * 參數檔。").append("\n");
		constant.append(" */").append("\n");
		constant.append("public interface ParmConstant {\n");
		path.append(getWorkRoot()).append("Tool");
		path.append(File.separator).append("src");
		path.append(File.separator).append("jcs");
		path.append(File.separator).append("tool");
		path.append(File.separator).append("ELOAN_ParmConstant.xlsx");
		File xlsx = new File(path.toString());
		try{	
			in = new FileInputStream(xlsx);
			wb = WorkbookFactory.create(in);
			Sheet sheet = wb.getSheetAt(0);
			String temp_type = null;
			for(Row row : sheet){
				int rowIndex = row.getRowNum();
				if(rowIndex > 1){
					String sys = row.getCell(0).toString();
					String type = row.getCell(1).toString();
					if(!sys.isEmpty() && type.isEmpty()){
						constant.append("	final String "+ sys +" = \"");
						temp_type = null;
					}else if(!sys.isEmpty() && !type.isEmpty() && temp_type == null){
						constant.append(type + "\";\n");
						temp_type = type;
					}else if(sys.isEmpty() && type.isEmpty()){
						System.out.println("發生錯誤：" + (rowIndex+1));
					}
				}
			}
			constant.append("}");
			path.delete(0, path.length());
			path.append(getWorkRoot()).append("ELOAN-COMMON");
			path.append(File.separator).append("src");
			path.append(File.separator).append("main");
			path.append(File.separator).append("java");
			path.append(File.separator).append("com");
			path.append(File.separator).append("system");
			path.append(File.separator).append("constant");
			path.append(File.separator).append("ParmConstant.java");
			System.out.println(path.toString());
			File java = new File(path.toString());
			if(java.exists())
				java.delete();
			java.getParentFile().mkdirs();
			fw = new FileWriter(path.toString());
			fw.write(constant.toString());
			fw.flush();				
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(wb != null)
					wb.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(in != null)
					in.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * XLSX轉換為DDL。
	 */
	private static void xlsxToDDL(){
		Workbook wb = null;
		InputStream in = null;
		FileWriter fw = null;
		StringBuilder path = new StringBuilder();
		StringBuilder ddl = new StringBuilder();
		ddl.append("-- 參數檔\n");
		ddl.append("DELETE FROM ELN.ELS0PAM;\n\n");
		path.append(getWorkRoot()).append("Tool");
		path.append(File.separator).append("src");
		path.append(File.separator).append("jcs");
		path.append(File.separator).append("tool");
		path.append(File.separator).append("ELOAN_ParmConstant.xlsx");
		File xlsx = new File(path.toString());
		try{	
			in = new FileInputStream(xlsx);
			wb = WorkbookFactory.create(in);
			Sheet sheet = wb.getSheetAt(0);
			for(Row row : sheet){
				int rowIndex = row.getRowNum();
				if(rowIndex > 1){
					if(!nulltoSpace(row.getCell(0)).isEmpty() && nulltoSpace(row.getCell(1)).isEmpty()){
						ddl.append("-- ").append(row.getCell(0).toString()).append("\n");
					}else if(!nulltoSpace(row.getCell(0)).isEmpty() && !nulltoSpace(row.getCell(1)).isEmpty()){
						String sys = parse(row.getCell(0), 5, String.class, null);
						String type = parse(row.getCell(1), 35, String.class, null);
						String pam1 = parse(row.getCell(2), 65, String.class, null);
						String pam2 = parse(row.getCell(3), 150, String.class, null);
						String pam3 = parse(row.getCell(4), 150, String.class, null);
						String isuse = parse(row.getCell(5), 5, String.class, "Y");
						String sort = parse(row.getCell(6), 10, Number.class, null);
						String maintain = parse(row.getCell(7), 10, String.class, null);
						String updater = parse(row.getCell(8), 10, String.class, "SYSTEM");
						String updtime = parse(row.getCell(9), 25, Date.class, "CURRENT TIMESTAMP");
						String pam2us = parse(row.getCell(10), 150, String.class, null);
						String pam3us = parse(row.getCell(11), 150, String.class, null).replaceAll(",", "");
						String sql = MessageFormat.format(pam_format, new Object[]{
								sys, type, pam1, pam2, pam3, isuse, sort, maintain, updater, updtime, pam2us, pam3us});
						ddl.append(sql + "\n");
					}
				}
			}
			path.delete(0, path.length());
			path.append(getWorkRoot()).append("DOC");
			path.append(File.separator).append("02、第一階段相關表格");
			path.append(File.separator).append("2、附件DDL");
			path.append(File.separator).append("data-els0pam.ddl");
			System.out.println(path.toString());
			File java = new File(path.toString());
			if(java.exists())
				java.delete();
			java.getParentFile().mkdirs();
			fw = new FileWriter(path.toString());
			fw.write(ddl.toString());
			fw.flush();				
		}catch(Exception e){
			e.printStackTrace();
		}finally{
			try{
				if(fw != null)
					fw.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(wb != null)
					wb.close();
			}catch(IOException e){
				e.printStackTrace();
			}
			try{
				if(in != null)
					in.close();
			}catch(IOException e){
				e.printStackTrace();
			}
		}
	}
	
	/** 解析cell */
	private static String parse(Cell cell, int len, Class<?> clazz, String defaultValue) throws ParseException{
		String result = "null";
		if(cell != null){
			String value = cell.getStringCellValue();
			if(clazz == String.class){
				if(!value.isEmpty() && !"(null)".equals(value)){
					result = "'" + value + "'";
				}else if(defaultValue != null){
					result = "'" + defaultValue + "'";
				}
			}else if(clazz == Date.class){
				if(!value.isEmpty() && !"(null)".equals(value)){
					result = "'" + formatDate2.format(formatDate1.parse(value)) + "'";
				}else if(defaultValue != null){
					result = defaultValue;
				}
			}else if(clazz == Number.class){
				if(!value.isEmpty() && !"(null)".equals(value)){
					result = value;
				}else if(defaultValue != null){
					result = defaultValue;
				}
			}
		}else{
			if(clazz == String.class){
				if(defaultValue != null){
					result = "'" + defaultValue + "'";
				}
			}else if(clazz == Date.class){
				if(defaultValue != null){
					result = defaultValue;
				}
			}else if(clazz == Number.class){
				if(defaultValue != null){
					result = defaultValue;
				}
			}
		}
		StringBuilder sb = new StringBuilder(result+",");
		for(int i = 0; i < (len - strLength(sb.toString().trim())); i++){
			sb.append(" ");
		}
		return sb.toString();
	}
	
	/** 計算字串長度 */
	private static int strLength(String src){
		int len = 0;
		for(int i = 0; i < src.length(); i++)
			len += (Integer.toHexString((int)src.charAt(i)).length() == 4) ? 2 : 1;
		return len;
	}
	
	/** 判斷null */
	private static String nulltoSpace(Cell cell){
		return (cell == null ? "" : cell.toString());
	}
}
