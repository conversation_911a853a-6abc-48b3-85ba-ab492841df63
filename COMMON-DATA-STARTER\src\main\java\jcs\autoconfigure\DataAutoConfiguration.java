package jcs.autoconfigure;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

/**
 * DataAutoConfiguration
 *
 * 提供共用的 Spring Data（JPA/JDBC）自動組態骨架，讓所有應用可重用。
 * - 僅在偵測到 Spring Data 類別存在時啟用（@ConditionalOnClass）
 * - 僅在應用端未自定義對應 Bean 時提供預設（@ConditionalOnMissingBean）
 *
 * 後續可在此補上：Repository 掃描、TransactionManager、JdbcTemplate 等 Bean。
 */
@AutoConfiguration
@ConditionalOnClass(name = {
        "org.springframework.data.jpa.repository.JpaRepository",
        "org.springframework.jdbc.core.JdbcTemplate"
})
public class DataAutoConfiguration {

    /**
     * 定義一個標記用的 Bean，確保 Starter 自動組態已生效。
     *
     * @return Object 標記物件（無副作用）
     */
    @Bean
    @ConditionalOnMissingBean(name = "commonDataMarker")
    public Object commonDataMarker() {
        return new Object();
    }
}

