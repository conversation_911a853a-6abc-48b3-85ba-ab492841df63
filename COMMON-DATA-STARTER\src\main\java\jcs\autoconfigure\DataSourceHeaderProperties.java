package jcs.autoconfigure;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * DataSourceHeaderProperties
 *
 * 以 HTTP Header 自動切換資料源的設定。
 * prefix: jcs.datasource.header
 *
 * 範例：
 * jcs.datasource.header.enabled=true
 * jcs.datasource.header.name=X-DS
 */
@ConfigurationProperties(prefix = "jcs.datasource.header")
public class DataSourceHeaderProperties {

    /** 是否啟用依 Header 切換資料源 */
    private boolean enabled = false;

    /** Header 名稱（預設：X-DS） */
    private String name = "X-DS";

    /**
     * 是否啟用。
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 設定是否啟用。
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * 取得 Header 名稱。
     */
    public String getName() {
        return name;
    }

    /**
     * 設定 Header 名稱。
     */
    public void setName(String name) {
        this.name = name;
    }
}

